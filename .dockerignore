# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
*.out

# Database
*.db
*.sqlite
*.sqlite3

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
.readthedocs.yml

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# UV
.uv-cache/

# Application specific
uploads/
backups/
ssl/
monitoring/
nginx/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Environment files
.env
.env.*
!.env.example

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Large files
*.iso
*.dmg

# Windows specific
*.exe
*.msi

# macOS specific
.AppleDouble
.LSOverride

# Linux specific
*~

# Editor backups
*.orig
*.rej

# Patch files
*.patch
*.diff

# Local configuration
local_settings.py
settings_local.py

# Secrets
secrets/
*.pem
*.key
*.crt

# Cache directories
.cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Profiling data
.prof

# PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
#.idea/
