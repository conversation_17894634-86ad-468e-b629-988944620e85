# Database Configuration
DATABASE_URL=sqlite:///./app.db

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# Amazon Product Advertising API Configuration
AMAZON_ACCESS_KEY=your-amazon-access-key
AMAZON_SECRET_KEY=your-amazon-secret-key
AMAZON_PARTNER_TAG=your-affiliate-tag
AMAZON_HOST=webservices.amazon.com
AMAZON_REGION=us-east-1

# ScrapeGraphAI Configuration
SCRAPEGRAPH_API_KEY=your-scrapegraph-api-key

# Redis Configuration (for rate limiting and caching)
REDIS_URL=redis://localhost:6379/0

# Application Configuration
APP_NAME=UK Cheap Deal Backend
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# Email Configuration (for user verification)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Scraping Configuration
SCRAPING_DELAY_MIN=1
SCRAPING_DELAY_MAX=3
SCRAPING_CONCURRENT_REQUESTS=5
SCRAPING_TIMEOUT=30

# File Upload Configuration
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_DIR=uploads/
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,webp
