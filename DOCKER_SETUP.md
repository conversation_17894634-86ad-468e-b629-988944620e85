# Docker Setup Guide

This guide explains how to use the cleaned up and optimized Docker setup for the UK Cheap Deal Backend.

## Overview

The Docker setup has been cleaned up and optimized with the following improvements:

- **Single Dockerfile**: Uses UV package manager for fast dependency installation
- **Multi-stage build**: Optimized for both development and production
- **Linux-only deployment**: Removed Windows compatibility for better performance
- **Simplified configurations**: Only dev and prod docker-compose files
- **Security optimized**: Non-root user, minimal attack surface

## Files Structure

```
├── Dockerfile                 # Main Dockerfile with UV support
├── docker-compose.dev.yml     # Development environment
├── docker-compose.prod.yml    # Production environment
└── scripts/
    ├── test-docker.sh         # Docker setup testing script
    └── docker-dev.sh          # Development helper script
```

## Quick Start

### Development Environment

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f app

# Stop environment
docker-compose -f docker-compose.dev.yml down
```

### Production Environment

```bash
# Set required environment variables
export DATABASE_URL="****************************************/dbname"
export SECRET_KEY="your-secret-key-at-least-32-characters"
export DB_PASSWORD="your-db-password"
export REDIS_PASSWORD="your-redis-password"
export AMAZON_ACCESS_KEY="your-amazon-access-key"
export AMAZON_SECRET_KEY="your-amazon-secret-key"
export AMAZON_PARTNER_TAG="your-amazon-partner-tag"

# Start production environment
docker-compose -f docker-compose.prod.yml up -d

# Stop environment
docker-compose -f docker-compose.prod.yml down
```

## Optional Services

### Testing

```bash
# Run tests
docker-compose -f docker-compose.dev.yml --profile test up test-runner
```

### PostgreSQL (Development)

```bash
# Start with PostgreSQL instead of SQLite
docker-compose -f docker-compose.dev.yml --profile postgres up -d
```

### Nginx (Production)

```bash
# Start with Nginx reverse proxy
docker-compose -f docker-compose.prod.yml --profile nginx up -d
```

### Database Backup (Production)

```bash
# Start with automated database backups
docker-compose -f docker-compose.prod.yml --profile backup up -d
```

## Testing the Setup

Run the comprehensive test script:

```bash
./scripts/test-docker.sh
```

This script will:
- Verify Docker installation
- Build both dev and prod images
- Validate docker-compose configurations
- Test development environment startup
- Show image sizes and security information

## Environment Variables

### Development
- `AMAZON_ACCESS_KEY` - Your Amazon PA-API access key
- `AMAZON_SECRET_KEY` - Your Amazon PA-API secret key
- `AMAZON_PARTNER_TAG` - Your Amazon partner tag

### Production (Required)
- `DATABASE_URL` - PostgreSQL connection string
- `SECRET_KEY` - JWT secret key (32+ characters)
- `DB_PASSWORD` - Database password
- `REDIS_PASSWORD` - Redis password
- `AMAZON_ACCESS_KEY` - Amazon PA-API access key
- `AMAZON_SECRET_KEY` - Amazon PA-API secret key
- `AMAZON_PARTNER_TAG` - Amazon partner tag

### Production (Optional)
- `SENTRY_DSN` - Sentry error tracking DSN
- `GRAFANA_PASSWORD` - Grafana admin password

## Key Features

### UV Package Manager
- Fast dependency resolution and installation
- Better caching and performance
- Replaces pip for all Python package management

### Multi-stage Build
- **Builder stage**: Contains build tools and dependencies
- **Production stage**: Minimal runtime image with only necessary components

### Security
- Non-root user execution
- Minimal base image (python:3.11-slim)
- No unnecessary packages in production
- Proper file permissions

### Performance
- Optimized layer caching
- Minimal image sizes
- Fast startup times
- Efficient resource usage

## Troubleshooting

### Common Issues

1. **Docker not running**: Ensure Docker Desktop is started
2. **Permission denied**: Make sure scripts are executable (`chmod +x scripts/*.sh`)
3. **Build failures**: Check if all required files are present
4. **Port conflicts**: Ensure ports 8000, 6379, 5432 are available

### Logs

```bash
# View application logs
docker-compose -f docker-compose.dev.yml logs app

# View all service logs
docker-compose -f docker-compose.dev.yml logs

# Follow logs in real-time
docker-compose -f docker-compose.dev.yml logs -f
```

### Debugging

```bash
# Access running container
docker-compose -f docker-compose.dev.yml exec app bash

# Check container status
docker-compose -f docker-compose.dev.yml ps

# Restart specific service
docker-compose -f docker-compose.dev.yml restart app
```

## Best Practices

1. **Use .env files** for environment variables in development
2. **Use Docker secrets** for sensitive data in production
3. **Regular updates** of base images and dependencies
4. **Monitor resource usage** in production
5. **Backup data volumes** regularly
6. **Use health checks** for service monitoring

## Next Steps

1. Set up CI/CD pipeline with Docker builds
2. Configure monitoring and logging
3. Set up automated backups
4. Implement blue-green deployments
5. Add security scanning to CI/CD
