# Local Development Setup with UV

This guide will help you set up a local development environment using UV package manager, bypassing Docker issues.

## Quick Setup (Automated)

Run the automated setup script:

```bash
./setup-local-dev.sh
```

This script will:

1. Install UV if not present
2. Create a virtual environment
3. Generate requirements.txt files
4. Install all dependencies
5. Create helper scripts

## Manual Setup (Step by Step)

If the automated script fails, follow these manual steps:

### Step 1: Install UV Package Manager

**macOS (with Homebrew):**

```bash
brew install uv
```

**macOS/Linux (with curl):**

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
export PATH="$HOME/.cargo/bin:$PATH"
```

**Windows (PowerShell):**

```powershell
irm https://astral.sh/uv/install.ps1 | iex
```

Verify installation:

```bash
uv --version
```

### Step 2: Create Virtual Environment

```bash
# Create virtual environment with Python 3.11
uv venv .venv --python 3.11

# Activate the environment
# On macOS/Linux:
source .venv/bin/activate

# On Windows:
.venv\Scripts\activate
```

### Step 3: Install Dependencies

```bash
# Install production dependencies
uv pip install -r requirements.txt

# Install development dependencies
uv pip install -r requirements-dev.txt

# Install project in editable mode
uv pip install -e .
```

### Step 4: Set Environment Variables

Create a `.env` file in the project root:

```bash
# .env file
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=sqlite:///./app.db
SECRET_KEY=dev-secret-key-change-in-production-must-be-at-least-32-characters-long
AMAZON_HOST=webservices.amazon.in
AMAZON_REGION=ap-south-1

# Replace with your actual Amazon API credentials
AMAZON_ACCESS_KEY=your-access-key
AMAZON_SECRET_KEY=your-secret-key
AMAZON_PARTNER_TAG=your-partner-tag
```

### Step 5: Run the Application

```bash
# Make sure virtual environment is activated
source .venv/bin/activate  # macOS/Linux
# or
.venv\Scripts\activate     # Windows

# Start the FastAPI development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## Helper Scripts

After running the setup script, you'll have these helper scripts:

### activate-env.sh

Activates the UV virtual environment:

```bash
source activate-env.sh
```

### run-local.sh

Starts the FastAPI development server with proper environment variables:

```bash
./run-local.sh
```

## Accessing the Application

Once the server is running, you can access:

- **API**: http://localhost:8000
- **Interactive API docs**: http://localhost:8000/docs
- **ReDoc documentation**: http://localhost:8000/redoc
- **Health check**: http://localhost:8000/health

## Development Commands

### Running Tests

```bash
# Run all tests
pytest tests/ -v

# Run tests with coverage
pytest tests/ -v --cov=app --cov-report=html

# Run specific test file
pytest tests/test_health.py -v
```

### Code Formatting

```bash
# Format code with Black
black app/ tests/

# Sort imports with isort
isort app/ tests/

# Check code style with flake8
flake8 app/ tests/
```

### Type Checking

```bash
# Run mypy type checking
mypy app/
```

## Troubleshooting

### Common Issues

1. **UV not found after installation**

   - Restart your terminal
   - Add UV to PATH: `export PATH="$HOME/.cargo/bin:$PATH"`
   - On Windows, restart PowerShell/Command Prompt

2. **Virtual environment activation fails**

   - Make sure you're in the project root directory
   - Check if `.venv` directory exists
   - Try recreating: `rm -rf .venv && uv venv .venv --python 3.11`

3. **Import errors when running the app**

   - Ensure virtual environment is activated
   - Reinstall in editable mode: `uv pip install -e .`
   - Check Python path: `which python` (should point to `.venv/bin/python`)

4. **Port 8000 already in use**

   - Kill existing process: `lsof -ti:8000 | xargs kill -9`
   - Or use different port: `uvicorn app.main:app --reload --port 8001`

5. **Database connection errors**

   - The app uses SQLite by default, which will be created automatically
   - Check if `app.db` file is created in the project root
   - For PostgreSQL, ensure the database server is running

6. **Amazon API errors**
   - Set proper credentials in `.env` file
   - The app will work without real credentials for basic functionality
   - Amazon API features will be disabled if credentials are invalid

### Dependency Issues

If you encounter dependency conflicts:

```bash
# Clear UV cache
uv cache clean

# Recreate virtual environment
rm -rf .venv
uv venv .venv --python 3.11
source .venv/bin/activate
uv pip install -r requirements.txt -r requirements-dev.txt
uv pip install -e .
```

### Performance Tips

1. **Use UV's caching**: UV automatically caches packages for faster installs
2. **Pin Python version**: Always use Python 3.11 for consistency
3. **Use .env files**: Keep environment variables in `.env` for easy management
4. **Enable hot reload**: Use `--reload` flag for development

## Environment Variables Reference

### Required for Development

- `ENVIRONMENT=development`
- `DEBUG=true`
- `DATABASE_URL=sqlite:///./app.db`
- `SECRET_KEY=<32+ character string>`

### Amazon API (Optional for basic testing)

- `AMAZON_HOST=webservices.amazon.in`
- `AMAZON_REGION=ap-south-1`
- `AMAZON_ACCESS_KEY=<your-access-key>`
- `AMAZON_SECRET_KEY=<your-secret-key>`
- `AMAZON_PARTNER_TAG=<your-partner-tag>`

### Optional

- `REDIS_URL=redis://localhost:6379/0` (if using Redis)
- `LOG_LEVEL=debug`

## Next Steps

1. **Set up your IDE**: Configure your editor to use the virtual environment
2. **Install pre-commit hooks**: `pre-commit install` (if using git)
3. **Configure debugging**: Set up your IDE's debugger to use the virtual environment
4. **Database migrations**: Run `alembic upgrade head` if needed
5. **API testing**: Use the interactive docs at `/docs` to test endpoints

## Getting Help

If you encounter issues:

1. Check the application logs in the terminal
2. Verify all environment variables are set correctly
3. Ensure the virtual environment is activated
4. Check the FastAPI documentation: https://fastapi.tiangolo.com/
5. Check UV documentation: https://github.com/astral-sh/uv
