# UK Cheap Deal Backend - FastAPI E-commerce Application

A modern, high-performance FastAPI-based e-commerce backend with Amazon Product Advertising API integration, built with UV package manager and Docker for streamlined development and deployment.

## 🚀 Features

- **FastAPI Framework**: Modern, fast web framework with automatic OpenAPI documentation
- **UV Package Manager**: Lightning-fast Python package installation and dependency management
- **Docker Support**: Separate development and production environments with Docker Compose
- **Amazon PA-API Integration**: Real-time product data from Amazon India marketplace
- **User Management**: Complete authentication system with JWT tokens and role-based access control
- **Product Management**: Advanced CRUD operations with filtering, search, and favorites
- **Admin Dashboard**: Comprehensive admin interface with user and store management
- **Rate Limiting**: Built-in API rate limiting and Redis caching
- **Database Integration**: SQLAlchemy ORM with SQLite (dev) and PostgreSQL (prod) support
- **Testing Suite**: Comprehensive test coverage with pytest and UV integration
- **Monitoring**: Health checks, metrics, and optional Prometheus/Grafana integration
- **Security**: JWT authentication, password hashing, and security middleware
- **Linux Optimized**: Designed specifically for Linux deployment environments

## 📋 Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended)
- **Python**: 3.8+ (Python 3.11+ recommended)
- **Docker**: 20.10+ with Docker Compose v2
- **Memory**: Minimum 2GB RAM (4GB+ recommended for development)
- **Storage**: 10GB+ available disk space

### API Credentials

- **Amazon Product Advertising API**: Configured for India region (ap-south-1)
  - Access Key ID
  - Secret Access Key
  - Partner Tag (Associate ID)
- **Optional**: Redis for caching and rate limiting
- **Optional**: ScrapeGraphAI API key for enhanced scraping capabilities

## 🚀 Quick Start

### Option 1: Docker Development Environment (Recommended)

The fastest way to get started with a complete development environment:

```bash
# Clone the repository
git clone <repository-url>
cd ukcheapdeal-backend-admin-dashboard-1

# Start development environment
chmod +x scripts/docker-dev.sh
./scripts/docker-dev.sh start

# Or use docker-compose directly
docker-compose -f docker-compose.dev.yml up -d
```

**Access Points:**

- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health
- Admin Panel: http://localhost:8000/admin
- Redis: localhost:6379

### Option 2: UV Package Manager (Native Development)

For native development with UV's lightning-fast package management:

```bash
# Clone the repository
git clone <repository-url>
cd ukcheapdeal-backend-admin-dashboard-1

# Install UV (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc

# Start with UV
chmod +x run_uv.sh start_uv.py
./run_uv.sh
# OR
python3 start_uv.py
```

### Option 3: Legacy pip Installation

For traditional pip-based installation:

```bash
# Clone the repository
git clone <repository-url>
cd ukcheapdeal-backend-admin-dashboard-1

# Create virtual environment
python3 -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -e ".[dev]"

# Run the application
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🐳 Docker Workflows

### Development Environment

```bash
# Start development environment with hot reload
./scripts/docker-dev.sh start

# View logs
./scripts/docker-dev.sh logs app

# Run tests
./scripts/docker-dev.sh test

# Start with PostgreSQL
./scripts/docker-dev.sh profile postgres

# Execute commands in container
./scripts/docker-dev.sh exec bash
./scripts/docker-dev.sh exec uv pip list

# Format code
./scripts/docker-dev.sh format

# Stop environment
./scripts/docker-dev.sh stop
```

### Production Deployment

```bash
# Set up environment variables
cp .env.example .env.prod
# Edit .env.prod with production values

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d

# With monitoring (Prometheus + Grafana)
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# With database backups
docker-compose -f docker-compose.prod.yml --profile backup up -d
```

## 🧪 Testing

### Running Tests with UV

```bash
# Install test dependencies
uv pip install -e ".[dev]"

# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html

# Run specific test categories
uv run pytest -m "unit"          # Unit tests only
uv run pytest -m "integration"   # Integration tests only
uv run pytest -m "not slow"      # Skip slow tests

# Run tests in parallel
uv run pytest -n auto
```

### Testing with Docker

```bash
# Run tests in Docker environment
./scripts/docker-dev.sh test

# Or with docker-compose
docker-compose -f docker-compose.dev.yml --profile test up test-runner
```

## 📁 Project Structure

```
├── app/                          # Main application package
│   ├── main.py                   # FastAPI application entry point
│   ├── config/
│   │   └── settings.py           # Application configuration
│   ├── models/                   # SQLAlchemy database models
│   │   ├── user.py               # User model with roles
│   │   ├── product.py            # Product model
│   │   ├── store.py              # Store model
│   │   └── ...
│   ├── schemas/                  # Pydantic request/response schemas
│   ├── services/                 # Business logic services
│   │   ├── auth_service.py       # Authentication service
│   │   ├── product_service.py    # Product management
│   │   ├── amazon_service.py     # Amazon PA-API integration
│   │   └── ...
│   ├── routes/                   # API route handlers
│   │   ├── auth.py               # Authentication endpoints
│   │   ├── products.py           # Product endpoints
│   │   ├── admin.py              # Admin endpoints
│   │   └── ...
│   ├── middleware/               # Custom middleware
│   ├── utils/                    # Utility functions and exceptions
│   └── database/                 # Database connection and configuration
├── tests/                        # Test suite
│   ├── conftest.py               # Test configuration and fixtures
│   ├── test_auth.py              # Authentication tests
│   ├── test_products.py          # Product tests
│   ├── test_admin.py             # Admin functionality tests
│   ├── test_health.py            # Health check tests
│   └── test_amazon_api.py        # Amazon API tests
├── scripts/                      # Utility scripts
│   ├── docker-dev.sh             # Docker development management
│   ├── init-db.sql               # Database initialization
│   └── backup.sh                 # Database backup script
├── docker-compose.dev.yml        # Development environment
├── docker-compose.prod.yml       # Production environment
├── pyproject.toml                # UV package configuration
├── Dockerfile                    # Multi-stage Docker build
└── README.md                     # This file
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Database Configuration
DATABASE_URL=sqlite:///./app.db                    # Development
# DATABASE_URL=postgresql://user:pass@localhost/db # Production

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# Amazon Product Advertising API (India Region)
AMAZON_ACCESS_KEY=your-amazon-access-key
AMAZON_SECRET_KEY=your-amazon-secret-key
AMAZON_PARTNER_TAG=your-affiliate-tag
AMAZON_HOST=webservices.amazon.in
AMAZON_REGION=ap-south-1

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Application Configuration
APP_NAME=UK Cheap Deal Backend
APP_VERSION=1.0.0
DEBUG=true                                          # Set to false in production
ENVIRONMENT=development                             # development/production/testing
```

### UV Configuration

The project uses `pyproject.toml` for dependency management with UV:

```toml
[project]
dependencies = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    # ... core dependencies
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "black>=23.11.0",
    # ... development dependencies
]
```

## 📚 API Documentation

Once the application is running, access the interactive documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### User Roles & Permissions

- **User**: View products, manage favorites, submit reviews
- **Admin**: All user permissions + product/store management (CRUD)
- **Superadmin**: Full system access including user management and system settings

## 🛠️ Development

### Code Quality

```bash
# Format code with UV
uv run black app/ tests/
uv run isort app/ tests/

# Lint code
uv run flake8 app/ tests/
uv run mypy app/

# Or use Docker development tools
./scripts/docker-dev.sh format
./scripts/docker-dev.sh lint
```

### Database Migrations

```bash
# Initialize Alembic (first time only)
uv run alembic init alembic

# Create migration
uv run alembic revision --autogenerate -m "Description of changes"

# Apply migrations
uv run alembic upgrade head

# Or in Docker
./scripts/docker-dev.sh exec alembic upgrade head
```

### Adding Dependencies

```bash
# Add production dependency
uv add fastapi

# Add development dependency
uv add --dev pytest

# Add optional dependency group
uv add --optional scraping beautifulsoup4

# Update pyproject.toml and sync
uv pip sync
```

## 🚀 Production Deployment

### Production Checklist

- [ ] Set `ENVIRONMENT=production` and `DEBUG=false`
- [ ] Use PostgreSQL database with connection pooling
- [ ] Configure Redis with authentication
- [ ] Set strong `SECRET_KEY` and JWT settings
- [ ] Set up SSL/TLS certificates
- [ ] Configure reverse proxy (Nginx)
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy
- [ ] Set resource limits and health checks

### Production Environment

```bash
# Create production environment file
cp .env.example .env.prod

# Edit with production values
nano .env.prod

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d

# Monitor deployment
docker-compose -f docker-compose.prod.yml logs -f app
```

### Monitoring & Observability

```bash
# Start with monitoring stack
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# Access monitoring tools
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000 (admin/admin)
```

## 🔍 Troubleshooting

### Common Issues

**UV Installation Issues:**

```bash
# Reinstall UV
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc
```

**Docker Permission Issues:**

```bash
# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

**Database Connection Issues:**

```bash
# Check database status
./scripts/docker-dev.sh exec app uv run alembic current
./scripts/docker-dev.sh logs postgres
```

**Port Already in Use:**

```bash
# Find and kill process using port 8000
sudo lsof -i :8000
sudo kill -9 <PID>
```

### Performance Optimization

- Use PostgreSQL for production (better performance than SQLite)
- Enable Redis caching for frequently accessed data
- Configure proper database indexes
- Use connection pooling
- Enable gzip compression in Nginx
- Set up CDN for static assets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Format code: `uv run black . && uv run isort .`
5. Run tests: `uv run pytest`
6. Commit changes: `git commit -m 'Add amazing feature'`
7. Push to branch: `git push origin feature/amazing-feature`
8. Submit a pull request

### Development Guidelines

- Follow PEP 8 style guidelines
- Write comprehensive tests for new features
- Update documentation for API changes
- Use type hints for better code clarity
- Keep functions small and focused
- Write descriptive commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) for the excellent web framework
- [UV](https://github.com/astral-sh/uv) for lightning-fast package management
- [Amazon Product Advertising API](https://webservices.amazon.com/paapi5/documentation/) for product data
- [Docker](https://www.docker.com/) for containerization support
