"""
Application configuration settings using Pydantic Settings.
"""
from typing import List, Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings
from functools import lru_cache
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application Configuration
    app_name: str = "UK Cheap Deal Backend"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # Database Configuration
    database_url: str = "sqlite:///./app.db"
    
    # Security Configuration
    secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30
    jwt_refresh_expire_days: int = 7
    
    # Amazon Product Advertising API Configuration
    amazon_access_key: Optional[str] = None
    amazon_secret_key: Optional[str] = None
    amazon_partner_tag: Optional[str] = None
    amazon_host: str = "webservices.amazon.com"
    amazon_region: str = "us-east-1"
    
    # ScrapeGraphAI Configuration
    scrapegraph_api_key: Optional[str] = None
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    
    # CORS Configuration
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: List[str] = ["*"]
    
    # Rate Limiting Configuration
    rate_limit_requests: int = 100
    rate_limit_period: int = 60
    
    # Email Configuration
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    from_email: Optional[str] = None
    
    # Logging Configuration
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # Scraping Configuration
    scraping_delay_min: int = 1
    scraping_delay_max: int = 3
    scraping_concurrent_requests: int = 5
    scraping_timeout: int = 30
    
    # File Upload Configuration
    max_file_size: int = 5242880  # 5MB
    upload_dir: str = "uploads/"
    allowed_image_extensions: List[str] = ["jpg", "jpeg", "png", "webp"]
    
    @field_validator("allowed_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @field_validator("allowed_methods", mode="before")
    @classmethod
    def parse_cors_methods(cls, v):
        """Parse CORS methods from string or list."""
        if isinstance(v, str):
            return [method.strip() for method in v.split(",")]
        return v

    @field_validator("allowed_image_extensions", mode="before")
    @classmethod
    def parse_image_extensions(cls, v):
        """Parse image extensions from string or list."""
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v

    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v):
        """Validate that secret key is provided and secure."""
        if not v:
            raise ValueError("SECRET_KEY must be provided")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


# Create logs directory if it doesn't exist
def ensure_directories():
    """Ensure required directories exist."""
    settings = get_settings()
    
    # Create logs directory
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Create upload directory
    if not os.path.exists(settings.upload_dir):
        os.makedirs(settings.upload_dir, exist_ok=True)


# Initialize directories on import
ensure_directories()
