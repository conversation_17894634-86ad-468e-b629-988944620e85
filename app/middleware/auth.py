"""
Authentication middleware for request processing.
"""
from typing import Callable, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.orm import Session

from app.database.connection import SessionLocal
from app.models.user import User
from app.utils.security import verify_token


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Middleware to add user context to requests."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and add user context."""
        
        # Initialize user context
        request.state.user = None
        request.state.is_authenticated = False
        
        # Extract token from Authorization header
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            
            # Verify token and get user
            user = await self._get_user_from_token(token)
            if user:
                request.state.user = user
                request.state.is_authenticated = True
        
        response = await call_next(request)
        return response
    
    async def _get_user_from_token(self, token: str) -> Optional[User]:
        """Get user from JW<PERSON> token."""
        try:
            payload = verify_token(token, "access")
            if payload is None:
                return None
            
            user_id = payload.get("sub")
            if user_id is None:
                return None
            
            # Get user from database
            db = SessionLocal()
            try:
                user = db.query(User).filter(User.id == user_id).first()
                if user and user.is_active:
                    return user
            finally:
                db.close()
            
            return None
        except Exception:
            return None


def get_current_user_from_request(request: Request) -> Optional[User]:
    """Get current user from request state."""
    return getattr(request.state, "user", None)


def is_authenticated(request: Request) -> bool:
    """Check if request is authenticated."""
    return getattr(request.state, "is_authenticated", False)
