"""
Rate limiting middleware using slowapi.
"""
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware

from app.config.settings import get_settings
from app.utils.exceptions import RateLimitException

settings = get_settings()

# Create limiter instance
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=[f"{settings.rate_limit_requests}/{settings.rate_limit_period}minute"]
)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Custom rate limiting middleware."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting to requests."""
        try:
            # Check if request should be rate limited
            if self._should_rate_limit(request):
                # Apply rate limiting
                await self._check_rate_limit(request)
            
            response = await call_next(request)
            return response
            
        except RateLimitExceeded as e:
            raise RateLimitException(
                detail=f"Rate limit exceeded: {e.detail}",
                headers={"Retry-After": str(e.retry_after)}
            )
    
    def _should_rate_limit(self, request: Request) -> bool:
        """Determine if request should be rate limited."""
        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
            return False
        
        # Skip rate limiting for static files
        if request.url.path.startswith("/static"):
            return False
        
        return True
    
    async def _check_rate_limit(self, request: Request):
        """Check rate limit for the request."""
        # This is a simplified implementation
        # In production, you would use Redis or another backend
        client_ip = get_remote_address(request)
        current_time = time.time()
        
        # For now, we'll just log the rate limit check
        # In a real implementation, you would check against a Redis store
        pass
