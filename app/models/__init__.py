"""
Database models module.
Import all models to ensure they are registered with SQLAlchemy.
"""
from app.models.base import BaseModel, TimestampMixin, SoftDeleteMixin
from app.models.user import User, UserRole
from app.models.store import Store
from app.models.category import Category
from app.models.product import Product, ProductPrice
from app.models.product_image import ProductImage
from app.models.review import Review
from app.models.affiliate_link import AffiliateLink
from app.models.user_favorite import UserFavorite
from app.models.scraping_job import ScrapingJob, ScrapingLog, JobStatus

__all__ = [
    "BaseModel",
    "TimestampMixin",
    "SoftDeleteMixin",
    "User",
    "UserRole",
    "Store",
    "Category",
    "Product",
    "ProductPrice",
    "ProductImage",
    "Review",
    "AffiliateLink",
    "UserFavorite",
    "ScrapingJob",
    "ScrapingLog",
    "JobStatus",
]
