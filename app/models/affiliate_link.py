"""
Affiliate link model for tracking affiliate marketing.
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, Boolean, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime

from app.models.base import BaseModel


class AffiliateLink(BaseModel):
    """Affiliate link model for tracking and analytics."""
    
    __tablename__ = "affiliate_links"
    
    # Product Reference
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Affiliate Information
    affiliate_id = Column(String(100), nullable=False)  # Partner tag or affiliate ID
    affiliate_program = Column(String(50), nullable=False)  # "amazon", "ebay", etc.
    
    # URLs
    original_url = Column(Text, nullable=False)
    tracking_url = Column(Text, nullable=False)
    short_url = Column(String(255), nullable=True)
    
    # Tracking Information
    click_count = Column(Integer, default=0, nullable=False)
    conversion_count = Column(Integer, default=0, nullable=False)
    last_clicked = Column(DateTime, nullable=True)
    
    # Configuration
    commission_rate = Column(String(10), nullable=True)  # e.g., "5%", "£2.50"
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime, nullable=True)
    
    # Relationships
    product = relationship("Product", back_populates="affiliate_links")
    
    @property
    def conversion_rate(self):
        """Calculate conversion rate."""
        if self.click_count > 0:
            return self.conversion_count / self.click_count
        return 0.0
    
    @property
    def is_expired(self):
        """Check if affiliate link is expired."""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    def increment_clicks(self):
        """Increment click count and update last clicked."""
        self.click_count += 1
        self.last_clicked = datetime.utcnow()
    
    def increment_conversions(self):
        """Increment conversion count."""
        self.conversion_count += 1
    
    def __repr__(self):
        return f"<AffiliateLink(id={self.id}, product_id={self.product_id}, program={self.affiliate_program})>"
