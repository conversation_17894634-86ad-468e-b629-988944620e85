"""
Category model for hierarchical product categorization.
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, Boolean
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Category(BaseModel):
    """Category model with hierarchical structure."""
    
    __tablename__ = "categories"
    
    # Basic Information
    name = Column(String(100), nullable=False)
    slug = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Hierarchy
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    level = Column(Integer, default=0, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    # Store Association
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    
    # External IDs for API integration
    external_id = Column(String(100), nullable=True)
    amazon_browse_node_id = Column(String(50), nullable=True)
    
    # Status
    is_active = Column(<PERSON>olean, default=True, nullable=False)
    
    # SEO
    meta_title = Column(String(255), nullable=True)
    meta_description = Column(Text, nullable=True)
    
    # Relationships
    store = relationship("Store", back_populates="categories")
    parent = relationship("Category", remote_side="Category.id", backref="children")
    products = relationship("Product", back_populates="category")
    
    @property
    def full_path(self):
        """Get full category path."""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name
    
    @property
    def is_leaf(self):
        """Check if category is a leaf node (has no children)."""
        return len(self.children) == 0
    
    def get_all_children(self):
        """Get all descendant categories."""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_all_children())
        return children
    
    def __repr__(self):
        return f"<Category(id={self.id}, name={self.name}, store_id={self.store_id})>"
