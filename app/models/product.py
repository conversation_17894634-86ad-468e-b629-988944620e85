"""
Product model and related models for e-commerce products.
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, Boolean, Float, DateTime, Numeric
from sqlalchemy.orm import relationship
from decimal import Decimal
from datetime import datetime

from app.models.base import BaseModel


class Product(BaseModel):
    """Main product model."""
    
    __tablename__ = "products"
    
    # Basic Information
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    short_description = Column(Text, nullable=True)
    
    # External References
    external_id = Column(String(100), nullable=True)  # Store's product ID
    asin = Column(String(20), nullable=True)  # Amazon ASIN
    sku = Column(String(100), nullable=True)
    
    # Store and Category
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # Product Details
    brand = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    manufacturer = Column(String(100), nullable=True)
    
    # Availability
    is_available = Column(Boolean, default=True, nullable=False)
    stock_quantity = Column(Integer, nullable=True)
    availability_status = Column(String(50), nullable=True)  # "In Stock", "Out of Stock", etc.
    
    # Shipping
    shipping_weight = Column(Float, nullable=True)
    shipping_dimensions = Column(String(100), nullable=True)  # "L x W x H"
    free_shipping = Column(Boolean, default=False, nullable=False)
    
    # SEO and URLs
    slug = Column(String(500), nullable=True)
    product_url = Column(Text, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    store = relationship("Store", back_populates="products")
    category = relationship("Category", back_populates="products")
    prices = relationship("ProductPrice", back_populates="product", cascade="all, delete-orphan")
    images = relationship("ProductImage", back_populates="product", cascade="all, delete-orphan")
    reviews = relationship("Review", back_populates="product", cascade="all, delete-orphan")
    affiliate_links = relationship("AffiliateLink", back_populates="product", cascade="all, delete-orphan")
    favorites = relationship("UserFavorite", back_populates="product", cascade="all, delete-orphan")
    
    @property
    def current_price(self):
        """Get the most recent price."""
        if self.prices:
            return max(self.prices, key=lambda p: p.updated_at)
        return None
    
    @property
    def primary_image(self):
        """Get the primary product image."""
        for image in self.images:
            if image.is_primary:
                return image
        return self.images[0] if self.images else None
    
    @property
    def average_rating(self):
        """Calculate average rating from reviews."""
        if not self.reviews:
            return 0.0
        return sum(review.rating for review in self.reviews) / len(self.reviews)
    
    @property
    def review_count(self):
        """Get total number of reviews."""
        return len(self.reviews)
    
    def __repr__(self):
        return f"<Product(id={self.id}, title={self.title[:50]}...)>"


class ProductPrice(BaseModel):
    """Product pricing information with history."""
    
    __tablename__ = "product_prices"
    
    # Product Reference
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Pricing
    current_price = Column(Numeric(10, 2), nullable=False)
    original_price = Column(Numeric(10, 2), nullable=True)
    currency = Column(String(3), default="GBP", nullable=False)
    
    # Discount Information
    discount_amount = Column(Numeric(10, 2), nullable=True)
    discount_percentage = Column(Float, nullable=True)
    
    # Price Source
    source = Column(String(50), nullable=True)  # "api", "scraping", "manual"
    
    # Relationships
    product = relationship("Product", back_populates="prices")
    
    @property
    def savings(self):
        """Calculate savings amount."""
        if self.original_price and self.current_price:
            return self.original_price - self.current_price
        return Decimal('0.00')
    
    @property
    def savings_percentage(self):
        """Calculate savings percentage."""
        if self.original_price and self.current_price and self.original_price > 0:
            return float((self.original_price - self.current_price) / self.original_price * 100)
        return 0.0
    
    def __repr__(self):
        return f"<ProductPrice(id={self.id}, product_id={self.product_id}, price={self.current_price})>"
