"""
Product image model for managing product images.
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, Boolean
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class ProductImage(BaseModel):
    """Product image model."""
    
    __tablename__ = "product_images"
    
    # Product Reference
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Image Information
    image_url = Column(Text, nullable=False)
    alt_text = Column(String(255), nullable=True)
    title = Column(String(255), nullable=True)
    
    # Image Properties
    is_primary = Column(Boolean, default=False, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    # Image Metadata
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    file_size = Column(Integer, nullable=True)  # in bytes
    format = Column(String(10), nullable=True)  # jpg, png, webp, etc.
    
    # Source Information
    source = Column(String(50), nullable=True)  # "api", "scraping", "upload"
    original_url = Column(Text, nullable=True)  # Original URL before processing
    
    # Relationships
    product = relationship("Product", back_populates="images")
    
    def __repr__(self):
        return f"<ProductImage(id={self.id}, product_id={self.product_id}, primary={self.is_primary})>"
