"""
Review model for product reviews and ratings.
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, Boolean, Float
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Review(BaseModel):
    """Product review model."""
    
    __tablename__ = "reviews"
    
    # References
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Nullable for scraped reviews
    
    # Review Content
    title = Column(String(255), nullable=True)
    comment = Column(Text, nullable=True)
    rating = Column(Float, nullable=False)  # 1.0 to 5.0
    
    # Review Metadata
    reviewer_name = Column(String(100), nullable=True)  # For scraped reviews
    verified_purchase = Column(Boolean, default=False, nullable=False)
    helpful_votes = Column(Integer, default=0, nullable=False)
    total_votes = Column(Integer, default=0, nullable=False)
    
    # Source Information
    source = Column(String(50), nullable=True)  # "user", "amazon", "scraping"
    external_id = Column(String(100), nullable=True)  # External review ID
    external_url = Column(Text, nullable=True)
    
    # Status
    is_approved = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    product = relationship("Product", back_populates="reviews")
    user = relationship("User", back_populates="reviews")
    
    @property
    def helpfulness_ratio(self):
        """Calculate helpfulness ratio."""
        if self.total_votes > 0:
            return self.helpful_votes / self.total_votes
        return 0.0
    
    @property
    def is_user_review(self):
        """Check if review is from a registered user."""
        return self.user_id is not None
    
    def __repr__(self):
        return f"<Review(id={self.id}, product_id={self.product_id}, rating={self.rating})>"
