"""
Scraping job model for tracking scraping operations.
"""
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Enum, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.models.base import BaseModel


class JobStatus(enum.Enum):
    """Scraping job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScrapingJob(BaseModel):
    """Scraping job model for tracking scraping operations."""
    
    __tablename__ = "scraping_jobs"
    
    # Job Information
    job_type = Column(String(50), nullable=False)  # "product_search", "product_details", etc.
    status = Column(Enum(JobStatus), default=JobStatus.PENDING, nullable=False)
    
    # Target Information
    target_url = Column(Text, nullable=True)
    target_store = Column(String(50), nullable=True)
    search_query = Column(String(255), nullable=True)
    
    # Configuration
    config = Column(JSON, nullable=True)  # Scraping configuration
    
    # Progress Tracking
    total_items = Column(Integer, default=0, nullable=False)
    processed_items = Column(Integer, default=0, nullable=False)
    successful_items = Column(Integer, default=0, nullable=False)
    failed_items = Column(Integer, default=0, nullable=False)
    
    # Timing
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Results
    result_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Logs
    logs = relationship("ScrapingLog", back_populates="job", cascade="all, delete-orphan")
    
    @property
    def progress_percentage(self):
        """Calculate progress percentage."""
        if self.total_items > 0:
            return (self.processed_items / self.total_items) * 100
        return 0.0
    
    @property
    def success_rate(self):
        """Calculate success rate."""
        if self.processed_items > 0:
            return (self.successful_items / self.processed_items) * 100
        return 0.0
    
    @property
    def duration(self):
        """Calculate job duration."""
        if self.started_at:
            end_time = self.completed_at or datetime.utcnow()
            return end_time - self.started_at
        return None
    
    def start(self):
        """Mark job as started."""
        self.status = JobStatus.RUNNING
        self.started_at = datetime.utcnow()
    
    def complete(self, result_data=None):
        """Mark job as completed."""
        self.status = JobStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        if result_data:
            self.result_data = result_data
    
    def fail(self, error_message):
        """Mark job as failed."""
        self.status = JobStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
    
    def __repr__(self):
        return f"<ScrapingJob(id={self.id}, type={self.job_type}, status={self.status.value})>"


class ScrapingLog(BaseModel):
    """Scraping log model for detailed logging."""
    
    __tablename__ = "scraping_logs"
    
    # Job Reference
    job_id = Column(Integer, ForeignKey("scraping_jobs.id"), nullable=False)
    
    # Log Information
    level = Column(String(10), nullable=False)  # INFO, WARNING, ERROR
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    
    # Context
    url = Column(Text, nullable=True)
    step = Column(String(100), nullable=True)
    
    # Relationships
    job = relationship("ScrapingJob", back_populates="logs")
    
    def __repr__(self):
        return f"<ScrapingLog(id={self.id}, job_id={self.job_id}, level={self.level})>"
