"""
Store model for e-commerce platform configurations.
"""
from sqlalchemy import Column, String, Text, Boolean, JSON
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Store(BaseModel):
    """Store model for different e-commerce platforms."""
    
    __tablename__ = "stores"
    
    # Basic Information
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Configuration
    base_url = Column(String(255), nullable=False)
    api_endpoint = Column(String(255), nullable=True)
    
    # API Configuration (stored as <PERSON>SON)
    api_config = Column(JSON, nullable=True)
    
    # Scraping Configuration (stored as JSO<PERSON>)
    scraping_config = Column(JSON, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    supports_api = Column(Boolean, default=False, nullable=False)
    supports_scraping = Column(<PERSON>olean, default=True, nullable=False)
    
    # Affiliate Configuration
    affiliate_config = Column(JSON, nullable=True)
    
    # Relationships
    categories = relationship("Category", back_populates="store", cascade="all, delete-orphan")
    products = relationship("Product", back_populates="store", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Store(id={self.id}, name={self.name}, active={self.is_active})>"
