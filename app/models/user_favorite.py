"""
User favorite model for managing user's favorite products.
"""
from sqlalchemy import <PERSON>umn, Integer, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class UserFavorite(BaseModel):
    """User favorite products model."""
    
    __tablename__ = "user_favorites"
    
    # References
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="favorites")
    product = relationship("Product", back_populates="favorites")
    
    # Ensure unique user-product combination
    __table_args__ = (
        UniqueConstraint('user_id', 'product_id', name='unique_user_product_favorite'),
    )
    
    def __repr__(self):
        return f"<UserFavorite(id={self.id}, user_id={self.user_id}, product_id={self.product_id})>"
