"""
Admin routes for administrative operations and analytics.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query, status, HTTPException
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.schemas.admin import (
    DashboardStatsResponse,
    DetailedUserAnalytics,
    DetailedProductAnalytics,
    SystemHealthResponse,
    AdminUserListResponse,
    DataExportRequest,
    DataExportResponse,
    BulkActionRequest,
    BulkActionResponse
)
from app.schemas.user import UserRoleUpdate, UserStatusUpdate, UserResponse
from app.services.admin_service import AdminService
from app.models.user import User, UserRole
from app.utils.dependencies import require_admin, require_superadmin
from app.utils.exceptions import NotFoundException, ValidationException

router = APIRouter()


@router.get("/dashboard", response_model=DashboardStatsResponse)
async def get_dashboard_stats(
    days: int = Query(30, ge=1, le=365, description="Number of days for statistics"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive dashboard statistics (Admin+ only).
    
    Returns overview statistics including:
    - User metrics (total, active, verified, new registrations)
    - Product metrics (total, featured, available, new additions)
    - Store and review statistics
    - Scraping operation metrics
    - Recent activity summaries
    
    **days**: Number of days to include in trend analysis (1-365)
    """
    admin_service = AdminService(db)
    stats = admin_service.get_dashboard_stats(days)
    return DashboardStatsResponse(**stats)


@router.get("/analytics/users", response_model=DetailedUserAnalytics)
async def get_user_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days for analytics"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get detailed user analytics (Admin+ only).
    
    Returns comprehensive user analytics including:
    - Registration trends over time
    - Top users by activity (favorites, reviews)
    - User engagement metrics
    - Role distribution analysis
    """
    admin_service = AdminService(db)
    analytics = admin_service.get_user_analytics(days)
    return DetailedUserAnalytics(**analytics)


@router.get("/analytics/products", response_model=DetailedProductAnalytics)
async def get_product_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days for analytics"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get detailed product analytics (Admin+ only).
    
    Returns comprehensive product analytics including:
    - Product creation trends
    - Top categories and brands
    - Most favorited products
    - Store performance metrics
    """
    admin_service = AdminService(db)
    analytics = admin_service.get_product_analytics(days)
    return DetailedProductAnalytics(**analytics)


@router.get("/system/health", response_model=SystemHealthResponse)
async def get_system_health(
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get system health information (Admin+ only).
    
    Returns system health metrics including:
    - Database connectivity and status
    - Table record counts
    - Recent activity indicators
    - System resource usage
    """
    admin_service = AdminService(db)
    health = admin_service.get_system_health()
    return SystemHealthResponse(**health)


@router.get("/users", response_model=List[AdminUserListResponse])
async def list_users_admin(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of users to return"),
    role: Optional[str] = Query(None, description="Filter by user role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_verified: Optional[bool] = Query(None, description="Filter by verified status"),
    search: Optional[str] = Query(None, description="Search by email or name"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    List all users with filtering and pagination (Admin+ only).
    
    Returns a paginated list of users with optional filtering by:
    - **role**: User role (user, admin, superadmin)
    - **is_active**: Active status
    - **is_verified**: Verification status
    - **search**: Search in email or name fields
    """
    query = db.query(User)
    
    # Apply filters
    if role:
        try:
            role_enum = UserRole(role.lower())
            query = query.filter(User.role == role_enum)
        except ValueError:
            raise ValidationException(f"Invalid role: {role}")
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    if is_verified is not None:
        query = query.filter(User.is_verified == is_verified)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            User.email.ilike(search_term) |
            User.first_name.ilike(search_term) |
            User.last_name.ilike(search_term)
        )
    
    users = query.order_by(User.created_at.desc()).offset(skip).limit(limit).all()
    return users


@router.put("/users/{user_id}/role", response_model=UserResponse)
async def update_user_role_admin(
    user_id: int,
    role_data: UserRoleUpdate,
    current_user: User = Depends(require_superadmin),
    db: Session = Depends(get_db)
):
    """
    Update user role (Superadmin only).
    
    Changes a user's role. Only superadmins can perform this operation.
    Superadmins cannot demote themselves.
    
    - **role**: New role (user, admin, superadmin)
    """
    admin_service = AdminService(db)
    user = admin_service.update_user_role(user_id, role_data, current_user)
    return user


@router.put("/users/{user_id}/status", response_model=UserResponse)
async def update_user_status_admin(
    user_id: int,
    status_data: UserStatusUpdate,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update user status (Admin+ only).
    
    Activates or deactivates a user account. Admins cannot modify
    superadmin accounts, and users cannot deactivate themselves.
    
    - **is_active**: Active status
    """
    admin_service = AdminService(db)
    user = admin_service.update_user_status(user_id, status_data, current_user)
    return user


@router.post("/export", response_model=DataExportResponse)
async def export_data(
    export_request: DataExportRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Export system data (Admin+ only).
    
    Exports various types of system data in different formats.
    
    - **export_type**: Type of data (users, products, reviews, scraping_jobs, analytics)
    - **format**: Export format (csv, json, xlsx)
    - **date_from/date_to**: Date range for export
    - **filters**: Additional filters
    
    Returns export information with download URL.
    """
    # This is a placeholder implementation
    # In production, this would generate actual export files
    
    import uuid
    from datetime import datetime, timedelta
    
    export_id = str(uuid.uuid4())
    
    # Mock export processing
    record_counts = {
        "users": db.query(User).count(),
        "products": 150,  # Mock count
        "reviews": 75,    # Mock count
        "scraping_jobs": 25,  # Mock count
        "analytics": 1
    }
    
    record_count = record_counts.get(export_request.export_type, 0)
    
    return DataExportResponse(
        export_id=export_id,
        export_type=export_request.export_type,
        format=export_request.format,
        status="completed",
        download_url=f"/api/v1/admin/exports/{export_id}/download",
        file_size=record_count * 100,  # Mock file size
        record_count=record_count,
        created_at=datetime.utcnow().isoformat(),
        expires_at=(datetime.utcnow() + timedelta(hours=24)).isoformat()
    )


@router.post("/bulk-actions", response_model=BulkActionResponse)
async def perform_bulk_action(
    action_request: BulkActionRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Perform bulk actions on multiple records (Admin+ only).
    
    Executes bulk operations on users, products, or reviews.
    
    - **action**: Action to perform (activate_users, feature_products, etc.)
    - **target_ids**: List of record IDs to target (max 1000)
    - **parameters**: Additional action parameters
    
    Returns summary of operation results.
    """
    import time
    start_time = time.time()
    
    successful_operations = 0
    failed_operations = 0
    errors = []
    warnings = []
    
    try:
        if action_request.action in ["activate_users", "deactivate_users"]:
            # User status bulk actions
            is_active = action_request.action == "activate_users"
            
            for user_id in action_request.target_ids:
                try:
                    user = db.query(User).filter(User.id == user_id).first()
                    if user:
                        # Check permissions
                        if user.is_superadmin and not current_user.is_superadmin:
                            errors.append(f"Cannot modify superadmin user {user_id}")
                            failed_operations += 1
                            continue
                        
                        if user.id == current_user.id and not is_active:
                            errors.append("Cannot deactivate your own account")
                            failed_operations += 1
                            continue
                        
                        user.is_active = is_active
                        successful_operations += 1
                    else:
                        errors.append(f"User {user_id} not found")
                        failed_operations += 1
                except Exception as e:
                    errors.append(f"Error updating user {user_id}: {str(e)}")
                    failed_operations += 1
            
            db.commit()
        
        elif action_request.action in ["feature_products", "unfeature_products"]:
            # Product feature bulk actions
            is_featured = action_request.action == "feature_products"
            
            from app.models.product import Product
            
            for product_id in action_request.target_ids:
                try:
                    product = db.query(Product).filter(Product.id == product_id).first()
                    if product:
                        product.is_featured = is_featured
                        successful_operations += 1
                    else:
                        errors.append(f"Product {product_id} not found")
                        failed_operations += 1
                except Exception as e:
                    errors.append(f"Error updating product {product_id}: {str(e)}")
                    failed_operations += 1
            
            db.commit()
        
        else:
            raise ValidationException(f"Bulk action {action_request.action} not implemented")
    
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk action failed: {str(e)}"
        )
    
    execution_time = time.time() - start_time
    
    return BulkActionResponse(
        action=action_request.action,
        total_targets=len(action_request.target_ids),
        successful_operations=successful_operations,
        failed_operations=failed_operations,
        errors=errors,
        warnings=warnings,
        execution_time_seconds=round(execution_time, 2)
    )
