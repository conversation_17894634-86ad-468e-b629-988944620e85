"""
Amazon PA-API integration routes.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.schemas.amazon import (
    AmazonSearchRequest,
    AmazonSearchResponse,
    AmazonProductResponse,
    AmazonBrowseNodeRequest,
    AmazonBrowseNodeResponse,
    AmazonVariationsResponse,
    AmazonBatchRequest,
    AmazonBatchResponse,
    AmazonAffiliateLinkRequest,
    AmazonAffiliateLinkResponse,
    AmazonImportRequest,
    AmazonImportResponse,
    AmazonSyncRequest,
    AmazonSyncResponse
)
from app.services.amazon_service import AmazonPAAPIService
from app.services.product_service import ProductService
from app.utils.dependencies import require_admin, get_optional_user
from app.models.user import User
from app.utils.exceptions import ExternalAPIException, ValidationException

router = APIRouter()


@router.post("/search", response_model=AmazonSearchResponse)
async def search_amazon_products(
    search_request: AmazonSearchRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Search for products on Amazon using PA-API (Admin+ only).
    
    Searches Amazon's product catalog using the provided keywords and filters.
    Returns product information including prices, images, and affiliate links.
    
    - **keywords**: Search terms
    - **search_index**: Amazon category to search in
    - **max_results**: Maximum number of results (1-10)
    - **sort_by**: Sort criteria
    - **min_price/max_price**: Price range filters
    """
    amazon_service = AmazonPAAPIService()
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        products = await amazon_service.search_products(
            keywords=search_request.keywords,
            search_index=search_request.search_index,
            max_results=search_request.max_results,
            sort_by=search_request.sort_by
        )
        
        # Convert to response format
        amazon_products = [AmazonProductResponse(**product) for product in products]
        
        return AmazonSearchResponse(
            products=amazon_products,
            total_results=len(amazon_products),
            search_terms=search_request.keywords,
            search_index=search_request.search_index
        )
        
    except ExternalAPIException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Amazon API error: {e.detail}"
        )


@router.get("/product/{asin}", response_model=AmazonProductResponse)
async def get_amazon_product(
    asin: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get detailed product information from Amazon by ASIN (Admin+ only).
    
    Retrieves comprehensive product details including:
    - Product title, description, and features
    - Pricing and availability
    - Images and dimensions
    - Customer ratings and reviews
    - Category information
    """
    amazon_service = AmazonPAAPIService()
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        product = await amazon_service.get_product_details(asin)
        
        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Product with ASIN {asin} not found on Amazon"
            )
        
        return AmazonProductResponse(**product)
        
    except ExternalAPIException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Amazon API error: {e.detail}"
        )


@router.post("/browse-node", response_model=AmazonBrowseNodeResponse)
async def get_amazon_browse_node(
    browse_request: AmazonBrowseNodeRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get Amazon browse node (category) information (Admin+ only).
    
    Retrieves category hierarchy information from Amazon including:
    - Category name and display information
    - Child categories
    - Parent category information
    """
    amazon_service = AmazonPAAPIService()
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        browse_node = await amazon_service.get_browse_nodes(browse_request.browse_node_id)
        return AmazonBrowseNodeResponse(**browse_node)
        
    except ExternalAPIException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Amazon API error: {e.detail}"
        )


@router.get("/product/{asin}/variations", response_model=AmazonVariationsResponse)
async def get_amazon_product_variations(
    asin: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get product variations for an Amazon product (Admin+ only).
    
    Retrieves all variations of a product (different colors, sizes, etc.)
    including pricing and availability for each variation.
    """
    amazon_service = AmazonPAAPIService()
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        variations = await amazon_service.get_product_variations(asin)
        
        return AmazonVariationsResponse(
            parent_asin=asin,
            variations=variations
        )
        
    except ExternalAPIException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Amazon API error: {e.detail}"
        )


@router.post("/batch", response_model=AmazonBatchResponse)
async def get_amazon_products_batch(
    batch_request: AmazonBatchRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get multiple Amazon products in a single batch request (Admin+ only).
    
    Efficiently retrieves product information for multiple ASINs.
    Maximum 10 ASINs per request.
    """
    amazon_service = AmazonPAAPIService()
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        products = await amazon_service.batch_get_products(batch_request.asins)
        
        found_asins = [product["asin"] for product in products]
        not_found_asins = [asin for asin in batch_request.asins if asin not in found_asins]
        
        amazon_products = [AmazonProductResponse(**product) for product in products]
        
        return AmazonBatchResponse(
            products=amazon_products,
            requested_asins=batch_request.asins,
            found_asins=found_asins,
            not_found_asins=not_found_asins
        )
        
    except ExternalAPIException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Amazon API error: {e.detail}"
        )


@router.post("/affiliate-link", response_model=AmazonAffiliateLinkResponse)
async def generate_amazon_affiliate_link(
    link_request: AmazonAffiliateLinkRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Generate Amazon affiliate link for a product (Admin+ only).
    
    Creates a properly formatted affiliate link with the configured
    partner tag for commission tracking.
    """
    amazon_service = AmazonPAAPIService()
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        affiliate_url = amazon_service.generate_affiliate_link(
            link_request.asin,
            link_request.additional_params
        )
        
        from datetime import datetime
        
        return AmazonAffiliateLinkResponse(
            asin=link_request.asin,
            affiliate_url=affiliate_url,
            partner_tag=amazon_service.partner_tag,
            created_at=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate affiliate link: {str(e)}"
        )


@router.post("/import", response_model=AmazonImportResponse)
async def import_amazon_product(
    import_request: AmazonImportRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Import an Amazon product to the local database (Admin+ only).
    
    Fetches product information from Amazon and creates a local product
    record with pricing, images, and affiliate links.
    """
    amazon_service = AmazonPAAPIService()
    product_service = ProductService(db)
    
    if not amazon_service.is_configured():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Amazon PA-API is not configured"
        )
    
    try:
        # Get product details from Amazon
        amazon_product = await amazon_service.get_product_details(import_request.asin)
        
        if not amazon_product:
            return AmazonImportResponse(
                success=False,
                asin=import_request.asin,
                message=f"Product with ASIN {import_request.asin} not found on Amazon"
            )
        
        # TODO: Implement actual product import logic
        # This would involve:
        # 1. Creating a Product record
        # 2. Creating ProductPrice records
        # 3. Creating ProductImage records
        # 4. Creating AffiliateLink records
        
        return AmazonImportResponse(
            success=True,
            product_id=None,  # Would be the actual product ID
            asin=import_request.asin,
            message="Product import functionality not yet implemented",
            warnings=["This is a placeholder implementation"]
        )
        
    except ExternalAPIException as e:
        return AmazonImportResponse(
            success=False,
            asin=import_request.asin,
            message=f"Amazon API error: {e.detail}"
        )


@router.get("/status")
async def get_amazon_api_status(
    current_user: User = Depends(require_admin)
):
    """
    Get Amazon PA-API configuration status (Admin+ only).
    
    Returns information about the current API configuration
    and connectivity status.
    """
    amazon_service = AmazonPAAPIService()
    
    return {
        "configured": amazon_service.is_configured(),
        "partner_tag": amazon_service.partner_tag if amazon_service.is_configured() else None,
        "region": amazon_service.region,
        "host": amazon_service.host,
        "has_credentials": bool(amazon_service.access_key and amazon_service.secret_key)
    }
