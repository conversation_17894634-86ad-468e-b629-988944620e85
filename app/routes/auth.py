"""
Authentication routes for user registration, login, and token management.
"""
from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.schemas.auth import (
    UserRegistration,
    UserLogin,
    TokenResponse,
    TokenRefresh,
    PasswordReset,
    PasswordResetConfirm,
    EmailVerification,
    ChangePassword
)
from app.schemas.user import UserResponse
from app.services.auth_service import AuthService
from app.utils.dependencies import get_current_verified_user
from app.models.user import User

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegistration,
    db: Session = Depends(get_db)
):
    """
    Register a new user account.
    
    - **email**: Valid email address (will be used for login)
    - **password**: Strong password (min 8 chars, uppercase, lowercase, digit, special char)
    - **first_name**: Optional first name
    - **last_name**: Optional last name
    - **phone**: Optional phone number
    
    Returns the created user information (without password).
    A verification email will be sent to the provided email address.
    """
    auth_service = AuthService(db)
    user = auth_service.register_user(user_data)
    return user


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: UserLogin,
    db: Session = Depends(get_db)
):
    """
    Authenticate user and return access tokens.
    
    - **email**: User's email address
    - **password**: User's password
    
    Returns access and refresh tokens for API authentication.
    """
    auth_service = AuthService(db)
    user = auth_service.authenticate_user(login_data)
    tokens = auth_service.create_tokens(user)
    return tokens


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    token_data: TokenRefresh,
    db: Session = Depends(get_db)
):
    """
    Refresh an access token using a refresh token.
    
    - **refresh_token**: Valid refresh token
    
    Returns new access and refresh tokens.
    """
    auth_service = AuthService(db)
    tokens = auth_service.refresh_token(token_data.refresh_token)
    return tokens


@router.post("/logout", status_code=status.HTTP_200_OK)
async def logout(
    current_user: User = Depends(get_current_verified_user)
):
    """
    Logout the current user.
    
    Note: Since we're using stateless JWT tokens, this endpoint
    primarily serves as a confirmation. In a production environment,
    you might want to implement token blacklisting.
    """
    return {"message": "Successfully logged out"}


@router.post("/verify-email", response_model=UserResponse)
async def verify_email(
    verification_data: EmailVerification,
    db: Session = Depends(get_db)
):
    """
    Verify user's email address using verification token.
    
    - **token**: Email verification token received via email
    
    Returns the updated user information.
    """
    auth_service = AuthService(db)
    user = auth_service.verify_email(verification_data.token)
    return user


@router.post("/request-password-reset", status_code=status.HTTP_200_OK)
async def request_password_reset(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
):
    """
    Request a password reset email.
    
    - **email**: User's email address
    
    If the email exists in the system, a password reset email will be sent.
    Always returns success to prevent email enumeration attacks.
    """
    auth_service = AuthService(db)
    auth_service.request_password_reset(reset_data.email)
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password", response_model=UserResponse)
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """
    Reset password using reset token.
    
    - **token**: Password reset token received via email
    - **new_password**: New strong password
    
    Returns the updated user information.
    """
    auth_service = AuthService(db)
    user = auth_service.reset_password(reset_data.token, reset_data.new_password)
    return user


@router.post("/change-password", response_model=UserResponse)
async def change_password(
    password_data: ChangePassword,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Change the current user's password.
    
    - **current_password**: Current password for verification
    - **new_password**: New strong password
    
    Returns the updated user information.
    """
    auth_service = AuthService(db)
    user = auth_service.change_password(
        current_user,
        password_data.current_password,
        password_data.new_password
    )
    return user


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_verified_user)
):
    """
    Get current authenticated user's information.
    
    Returns the current user's profile information.
    """
    return current_user
