"""
Health check routes for monitoring and diagnostics.
"""
from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.services.health_service import health_service
from app.utils.dependencies import require_admin
from app.utils.error_handlers import error_tracker
from app.models.user import User

router = APIRouter()


@router.get("/health")
async def health_check():
    """
    Basic health check endpoint.
    
    Returns basic application health status.
    Used by load balancers and monitoring systems.
    """
    return health_service.get_basic_health()


@router.get("/health/detailed")
async def detailed_health_check(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Detailed health check endpoint (admin only).
    
    Returns comprehensive health information including:
    - Database connectivity
    - System resources
    - Application metrics
    
    Requires admin authentication.
    """
    return health_service.get_detailed_health(db)


@router.get("/health/ready")
async def readiness_check(db: Session = Depends(get_db)):
    """
    Readiness check endpoint.
    
    Checks if the application is ready to serve requests.
    Used by Kubernetes readiness probes.
    """
    result = health_service.get_readiness_check(db)
    
    if result["status"] == "ready":
        return result
    else:
        return result, status.HTTP_503_SERVICE_UNAVAILABLE


@router.get("/health/live")
async def liveness_check():
    """
    Liveness check endpoint.
    
    Checks if the application is alive and responsive.
    Used by Kubernetes liveness probes.
    """
    return health_service.get_liveness_check()


@router.get("/health/errors")
async def error_statistics(
    current_user: User = Depends(require_admin)
):
    """
    Get error statistics (admin only).
    
    Returns information about recent errors and error counts.
    Useful for monitoring and debugging.
    """
    return error_tracker.get_error_stats()
