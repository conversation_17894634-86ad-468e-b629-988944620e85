"""
Product management routes with CRUD operations and search.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductResponse,
    ProductListResponse,
    ProductSearchFilters,
    ProductListRequest
)
from app.services.product_service import ProductService
from app.utils.dependencies import get_optional_user, require_admin
from app.models.user import User
from app.utils.exceptions import NotFoundException

router = APIRouter()


@router.get("/", response_model=List[ProductListResponse])
async def list_products(
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of items to return"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    
    # Filter parameters
    query: Optional[str] = Query(None, description="Search query"),
    category_id: Optional[int] = Query(None, description="Filter by category"),
    store_id: Optional[int] = Query(None, description="Filter by store"),
    brand: Optional[str] = Query(None, description="Filter by brand"),
    min_price: Optional[float] = Query(None, ge=0, description="Minimum price"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price"),
    min_rating: Optional[float] = Query(None, ge=0, le=5, description="Minimum rating"),
    is_available: Optional[bool] = Query(None, description="Filter by availability"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    free_shipping: Optional[bool] = Query(None, description="Filter by free shipping"),
    
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_user)
):
    """
    Get a list of products with filtering, sorting, and pagination.
    
    Supports various filters:
    - **query**: Search in title, description, and brand
    - **category_id**: Filter by category
    - **store_id**: Filter by store
    - **brand**: Filter by brand name
    - **min_price/max_price**: Price range filter
    - **min_rating**: Minimum average rating
    - **is_available**: Availability filter
    - **is_featured**: Featured products filter
    - **free_shipping**: Free shipping filter
    
    Sorting options:
    - **sort_by**: Field to sort by (created_at, title, brand, price, rating)
    - **sort_order**: asc or desc
    """
    # Create filters object
    filters = ProductSearchFilters(
        query=query,
        category_id=category_id,
        store_id=store_id,
        brand=brand,
        min_price=min_price,
        max_price=max_price,
        min_rating=min_rating,
        is_available=is_available,
        is_featured=is_featured,
        free_shipping=free_shipping
    )
    
    product_service = ProductService(db)
    products, total_count = product_service.search_products(
        filters=filters,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    # Convert to list response format
    product_list = []
    for product in products:
        product_dict = {
            "id": product.id,
            "title": product.title,
            "short_description": product.short_description,
            "brand": product.brand,
            "is_available": product.is_available,
            "is_featured": product.is_featured,
            "average_rating": product.average_rating,
            "review_count": product.review_count,
            "slug": product.slug,
            "created_at": product.created_at,
            "store_name": product.store.display_name if product.store else None,
            "category_name": product.category.name if product.category else None,
            "current_price": product.current_price.current_price if product.current_price else None,
            "currency": product.current_price.currency if product.current_price else None,
            "primary_image_url": product.primary_image.image_url if product.primary_image else None
        }
        product_list.append(ProductListResponse(**product_dict))
    
    return product_list


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_user)
):
    """
    Get detailed information about a specific product.
    
    Returns comprehensive product information including:
    - Basic product details
    - Store and category information
    - Current and historical pricing
    - Product images
    - Average rating and review count
    """
    product_service = ProductService(db)
    product = product_service.get_product_by_id(product_id)
    
    if not product:
        raise NotFoundException("Product not found")
    
    return product


@router.post("/", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_data: ProductCreate,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create a new product (Admin+ only).
    
    Creates a new product with the provided information.
    Optionally includes initial pricing and images.
    
    Required fields:
    - **title**: Product title
    - **store_id**: ID of the store this product belongs to
    
    Optional fields include product details, pricing, images, and metadata.
    """
    product_service = ProductService(db)
    product = product_service.create_product(product_data)
    return product


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: int,
    product_data: ProductUpdate,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update an existing product (Admin+ only).
    
    Updates the specified product with the provided information.
    Only provided fields will be updated.
    """
    product_service = ProductService(db)
    product = product_service.update_product(product_id, product_data)
    return product


@router.delete("/{product_id}", status_code=status.HTTP_200_OK)
async def delete_product(
    product_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete a product (Admin+ only).
    
    Performs a soft delete by setting the product as inactive.
    The product data is preserved but will not appear in listings.
    """
    product_service = ProductService(db)
    success = product_service.delete_product(product_id)
    
    if not success:
        raise NotFoundException("Product not found")
    
    return {"message": "Product deleted successfully"}


@router.get("/search/suggestions")
async def get_search_suggestions(
    q: str = Query(..., min_length=2, description="Search query"),
    limit: int = Query(10, ge=1, le=20, description="Number of suggestions"),
    db: Session = Depends(get_db)
):
    """
    Get search suggestions based on query.
    
    Returns suggestions for:
    - Product titles
    - Brand names
    - Categories
    """
    # This is a simplified implementation
    # In production, you might want to use a dedicated search engine
    
    from app.models.product import Product
    from app.models.category import Category
    
    suggestions = []
    
    # Product title suggestions
    products = (
        db.query(Product.title)
        .filter(Product.title.ilike(f"%{q}%"))
        .filter(Product.is_active == True)
        .distinct()
        .limit(limit // 2)
        .all()
    )
    
    for product in products:
        suggestions.append({
            "type": "product",
            "text": product.title,
            "category": "Products"
        })
    
    # Brand suggestions
    brands = (
        db.query(Product.brand)
        .filter(Product.brand.ilike(f"%{q}%"))
        .filter(Product.brand.isnot(None))
        .filter(Product.is_active == True)
        .distinct()
        .limit(limit // 4)
        .all()
    )
    
    for brand in brands:
        suggestions.append({
            "type": "brand",
            "text": brand.brand,
            "category": "Brands"
        })
    
    # Category suggestions
    categories = (
        db.query(Category.name)
        .filter(Category.name.ilike(f"%{q}%"))
        .filter(Category.is_active == True)
        .distinct()
        .limit(limit // 4)
        .all()
    )
    
    for category in categories:
        suggestions.append({
            "type": "category",
            "text": category.name,
            "category": "Categories"
        })
    
    return {"suggestions": suggestions[:limit]}
