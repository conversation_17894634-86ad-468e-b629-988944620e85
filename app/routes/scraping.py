"""
Scraping routes for AI-powered web scraping operations.
"""
import asyncio
from typing import List, Optional
from fastapi import APIRouter, Depends, BackgroundTasks, Query, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.schemas.scraping import (
    ScrapingJobCreate,
    ProductSearchRequest,
    ScrapingJobResponse,
    ScrapingJobListResponse,
    ScrapingLogResponse,
    BatchScrapingRequest,
    ScrapingStatsResponse,
    PriceMonitoringRequest,
    CategoryScrapingRequest,
    ScrapingJobResult
)
from app.services.scraping_service import ScrapeGraphAIService
from app.models.scraping_job import ScrapingJob, ScrapingLog, JobStatus
from app.utils.dependencies import require_admin
from app.models.user import User
from app.utils.exceptions import NotFoundException, ValidationException

router = APIRouter()


@router.post("/products", response_model=ScrapingJobResult)
async def scrape_products(
    search_request: ProductSearchRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Initiate AI-powered product scraping across multiple platforms (Admin+ only).
    
    Searches for products across specified e-commerce platforms using
    AI-powered scraping with anti-detection measures.
    
    - **search_query**: Product search terms
    - **target_stores**: List of stores to search (amazon, ebay, walmart, etc.)
    - **max_results_per_store**: Maximum results per store (1-50)
    - **filters**: Additional search filters
    
    Returns immediate results for demonstration, but in production would
    return job ID for async processing.
    """
    async with ScrapeGraphAIService(db) as scraping_service:
        # Create a job for the first store as demonstration
        job = await scraping_service.create_scraping_job(
            job_type="product_search",
            target_store=search_request.target_stores[0],
            search_query=search_request.search_query,
            config={
                "max_results": search_request.max_results_per_store,
                "filters": search_request.filters
            }
        )
        
        # Process the job immediately for demonstration
        result = await scraping_service.process_scraping_job(job.id)
        
        return ScrapingJobResult(
            job_id=job.id,
            status=result["status"],
            results_count=result["results_count"],
            results=result["results"],
            execution_time_seconds=2.5,  # Mock execution time
            errors=[],
            warnings=[]
        )


@router.post("/jobs", response_model=ScrapingJobResponse, status_code=status.HTTP_201_CREATED)
async def create_scraping_job(
    job_data: ScrapingJobCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create a new scraping job (Admin+ only).
    
    Creates a scraping job that will be processed asynchronously.
    Use the job ID to check status and retrieve results.
    
    - **job_type**: Type of scraping (product_search, product_details, etc.)
    - **target_url**: Specific URL to scrape (optional)
    - **target_store**: Store to scrape (amazon, ebay, walmart, etc.)
    - **search_query**: Search terms (for product_search jobs)
    - **config**: Additional configuration options
    """
    async with ScrapeGraphAIService(db) as scraping_service:
        job = await scraping_service.create_scraping_job(
            job_type=job_data.job_type,
            target_url=job_data.target_url,
            target_store=job_data.target_store,
            search_query=job_data.search_query,
            config=job_data.config
        )
        
        # Schedule background processing
        background_tasks.add_task(process_scraping_job_background, job.id, db)
        
        return scraping_service.get_job_status(job.id)


@router.get("/jobs/{job_id}", response_model=ScrapingJobResponse)
async def get_scraping_job_status(
    job_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get scraping job status and progress (Admin+ only).
    
    Returns detailed information about a scraping job including:
    - Current status and progress
    - Success/failure rates
    - Execution time
    - Error messages (if any)
    """
    async with ScrapeGraphAIService(db) as scraping_service:
        return scraping_service.get_job_status(job_id)


@router.get("/jobs", response_model=List[ScrapingJobListResponse])
async def list_scraping_jobs(
    skip: int = Query(0, ge=0, description="Number of jobs to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of jobs to return"),
    status: Optional[str] = Query(None, description="Filter by job status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    target_store: Optional[str] = Query(None, description="Filter by target store"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    List scraping jobs with filtering and pagination (Admin+ only).
    
    Returns a list of scraping jobs with optional filtering by:
    - **status**: Job status (pending, running, completed, failed)
    - **job_type**: Type of scraping job
    - **target_store**: Target store name
    """
    query = db.query(ScrapingJob)
    
    if status:
        try:
            status_enum = JobStatus(status.lower())
            query = query.filter(ScrapingJob.status == status_enum)
        except ValueError:
            raise ValidationException(f"Invalid status: {status}")
    
    if job_type:
        query = query.filter(ScrapingJob.job_type == job_type)
    
    if target_store:
        query = query.filter(ScrapingJob.target_store == target_store.lower())
    
    jobs = query.order_by(ScrapingJob.created_at.desc()).offset(skip).limit(limit).all()
    
    return jobs


@router.get("/jobs/{job_id}/logs", response_model=List[ScrapingLogResponse])
async def get_scraping_job_logs(
    job_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    level: Optional[str] = Query(None, description="Filter by log level"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get logs for a specific scraping job (Admin+ only).
    
    Returns detailed logs for debugging and monitoring scraping operations.
    Logs include timestamps, levels, messages, and contextual information.
    """
    # Verify job exists
    job = db.query(ScrapingJob).filter(ScrapingJob.id == job_id).first()
    if not job:
        raise NotFoundException("Scraping job not found")
    
    query = db.query(ScrapingLog).filter(ScrapingLog.job_id == job_id)
    
    if level:
        query = query.filter(ScrapingLog.level == level.upper())
    
    logs = query.order_by(ScrapingLog.created_at.desc()).offset(skip).limit(limit).all()
    
    return logs


@router.post("/batch", response_model=List[ScrapingJobResponse])
async def create_batch_scraping_jobs(
    batch_request: BatchScrapingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create multiple scraping jobs in batch (Admin+ only).
    
    Creates multiple scraping jobs that will be processed asynchronously.
    Useful for bulk operations across multiple stores or categories.
    
    - **jobs**: List of job configurations (max 10)
    - **priority**: Job priority level
    """
    created_jobs = []
    
    async with ScrapeGraphAIService(db) as scraping_service:
        for job_data in batch_request.jobs:
            job = await scraping_service.create_scraping_job(
                job_type=job_data.job_type,
                target_url=job_data.target_url,
                target_store=job_data.target_store,
                search_query=job_data.search_query,
                config=job_data.config
            )
            
            # Schedule background processing
            background_tasks.add_task(process_scraping_job_background, job.id, db)
            
            created_jobs.append(scraping_service.get_job_status(job.id))
    
    return created_jobs


@router.get("/stats", response_model=ScrapingStatsResponse)
async def get_scraping_statistics(
    days: int = Query(30, ge=1, le=365, description="Number of days to include in stats"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get scraping operation statistics (Admin+ only).
    
    Returns comprehensive statistics about scraping operations including:
    - Job counts by status and type
    - Success rates and performance metrics
    - Store-specific statistics
    """
    from datetime import datetime, timedelta
    from sqlalchemy import func
    
    # Calculate date range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Base query for the date range
    base_query = db.query(ScrapingJob).filter(
        ScrapingJob.created_at >= start_date,
        ScrapingJob.created_at <= end_date
    )
    
    # Total jobs
    total_jobs = base_query.count()
    
    # Jobs by status
    completed_jobs = base_query.filter(ScrapingJob.status == JobStatus.COMPLETED).count()
    failed_jobs = base_query.filter(ScrapingJob.status == JobStatus.FAILED).count()
    running_jobs = base_query.filter(ScrapingJob.status == JobStatus.RUNNING).count()
    pending_jobs = base_query.filter(ScrapingJob.status == JobStatus.PENDING).count()
    
    # Success rate
    success_rate = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0
    
    # Average duration (mock calculation)
    average_duration_minutes = 2.5
    
    # Total products scraped
    total_products_scraped = completed_jobs * 15  # Mock calculation
    
    # Jobs by store
    jobs_by_store = {}
    store_stats = (
        base_query
        .filter(ScrapingJob.target_store.isnot(None))
        .with_entities(ScrapingJob.target_store, func.count(ScrapingJob.id))
        .group_by(ScrapingJob.target_store)
        .all()
    )
    for store, count in store_stats:
        jobs_by_store[store] = count
    
    # Jobs by type
    jobs_by_type = {}
    type_stats = (
        base_query
        .with_entities(ScrapingJob.job_type, func.count(ScrapingJob.id))
        .group_by(ScrapingJob.job_type)
        .all()
    )
    for job_type, count in type_stats:
        jobs_by_type[job_type] = count
    
    return ScrapingStatsResponse(
        total_jobs=total_jobs,
        completed_jobs=completed_jobs,
        failed_jobs=failed_jobs,
        running_jobs=running_jobs,
        pending_jobs=pending_jobs,
        success_rate=round(success_rate, 2),
        average_duration_minutes=average_duration_minutes,
        total_products_scraped=total_products_scraped,
        jobs_by_store=jobs_by_store,
        jobs_by_type=jobs_by_type
    )


@router.delete("/jobs/{job_id}")
async def cancel_scraping_job(
    job_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Cancel a running or pending scraping job (Admin+ only).
    
    Cancels a scraping job if it's still pending or running.
    Completed or failed jobs cannot be cancelled.
    """
    job = db.query(ScrapingJob).filter(ScrapingJob.id == job_id).first()
    if not job:
        raise NotFoundException("Scraping job not found")
    
    if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
        raise ValidationException(f"Cannot cancel job with status: {job.status.value}")
    
    job.status = JobStatus.CANCELLED
    job.error_message = "Job cancelled by user"
    db.commit()
    
    return {"message": "Scraping job cancelled successfully"}


async def process_scraping_job_background(job_id: int, db: Session):
    """Background task to process scraping job."""
    try:
        async with ScrapeGraphAIService(db) as scraping_service:
            await scraping_service.process_scraping_job(job_id)
    except Exception as e:
        # Log error but don't raise to avoid breaking background task
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Background scraping job {job_id} failed: {e}")
