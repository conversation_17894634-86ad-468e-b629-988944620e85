"""
User management routes for profile and favorites.
"""
from typing import List
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.schemas.user import UserUpdate, UserResponse
from app.schemas.product import ProductResponse
from app.models.user import User
from app.models.product import Product
from app.models.user_favorite import UserFavorite
from app.utils.dependencies import get_current_verified_user
from app.utils.exceptions import NotFoundException, ConflictException

router = APIRouter()


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_verified_user)
):
    """
    Get the current user's profile information.
    
    Returns detailed profile information for the authenticated user.
    """
    return current_user


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    profile_data: UserUpdate,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Update the current user's profile information.
    
    - **first_name**: First name
    - **last_name**: Last name
    - **phone**: Phone number
    - **address**: Street address
    - **city**: City
    - **country**: Country
    - **postal_code**: Postal/ZIP code
    
    Returns the updated user profile.
    """
    # Update user fields
    update_data = profile_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    
    return current_user


@router.get("/favorites", response_model=List[ProductResponse])
async def get_user_favorites(
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of items to return")
):
    """
    Get the current user's favorite products.
    
    Returns a paginated list of the user's favorite products.
    """
    favorites = (
        db.query(Product)
        .join(UserFavorite)
        .filter(UserFavorite.user_id == current_user.id)
        .offset(skip)
        .limit(limit)
        .all()
    )
    
    return favorites


@router.post("/favorites/{product_id}", status_code=status.HTTP_201_CREATED)
async def add_to_favorites(
    product_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Add a product to the user's favorites.
    
    - **product_id**: ID of the product to add to favorites
    
    Returns success message.
    """
    # Check if product exists
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise NotFoundException("Product not found")
    
    # Check if already in favorites
    existing_favorite = (
        db.query(UserFavorite)
        .filter(
            UserFavorite.user_id == current_user.id,
            UserFavorite.product_id == product_id
        )
        .first()
    )
    
    if existing_favorite:
        raise ConflictException("Product is already in favorites")
    
    # Add to favorites
    favorite = UserFavorite(
        user_id=current_user.id,
        product_id=product_id
    )
    
    db.add(favorite)
    db.commit()
    
    return {"message": "Product added to favorites"}


@router.delete("/favorites/{product_id}", status_code=status.HTTP_200_OK)
async def remove_from_favorites(
    product_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Remove a product from the user's favorites.
    
    - **product_id**: ID of the product to remove from favorites
    
    Returns success message.
    """
    # Find and remove favorite
    favorite = (
        db.query(UserFavorite)
        .filter(
            UserFavorite.user_id == current_user.id,
            UserFavorite.product_id == product_id
        )
        .first()
    )
    
    if not favorite:
        raise NotFoundException("Product not found in favorites")
    
    db.delete(favorite)
    db.commit()
    
    return {"message": "Product removed from favorites"}


@router.get("/favorites/check/{product_id}")
async def check_favorite_status(
    product_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Check if a product is in the user's favorites.
    
    - **product_id**: ID of the product to check
    
    Returns the favorite status.
    """
    favorite = (
        db.query(UserFavorite)
        .filter(
            UserFavorite.user_id == current_user.id,
            UserFavorite.product_id == product_id
        )
        .first()
    )
    
    return {"is_favorite": favorite is not None}
