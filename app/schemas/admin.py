"""
Admin-related Pydantic schemas.
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
from app.models.user import UserRole


class DashboardOverview(BaseModel):
    """Schema for dashboard overview statistics."""
    total_users: int
    active_users: int
    verified_users: int
    new_users: int
    total_products: int
    featured_products: int
    available_products: int
    new_products: int
    total_stores: int
    total_reviews: int
    approved_reviews: int
    average_rating: float


class UserAnalytics(BaseModel):
    """Schema for user analytics."""
    role_distribution: Dict[str, int]
    verification_rate: float
    activation_rate: float


class ProductAnalytics(BaseModel):
    """Schema for product analytics."""
    availability_rate: float
    featured_rate: float
    store_distribution: Dict[str, int]


class ScrapingAnalytics(BaseModel):
    """Schema for scraping analytics."""
    total_jobs: int
    success_rate: float
    failure_rate: float


class RecentUser(BaseModel):
    """Schema for recent user activity."""
    id: int
    email: str
    full_name: str
    role: str
    created_at: str


class RecentProduct(BaseModel):
    """Schema for recent product activity."""
    id: int
    title: str
    brand: Optional[str]
    is_featured: bool
    created_at: str


class RecentActivity(BaseModel):
    """Schema for recent activity."""
    recent_users: List[RecentUser]
    recent_products: List[RecentProduct]


class DashboardStatsResponse(BaseModel):
    """Schema for dashboard statistics response."""
    overview: DashboardOverview
    user_analytics: UserAnalytics
    product_analytics: ProductAnalytics
    scraping_analytics: ScrapingAnalytics
    recent_activity: RecentActivity


class RegistrationTrend(BaseModel):
    """Schema for user registration trend."""
    date: str
    registrations: int


class TopUserByFavorites(BaseModel):
    """Schema for top user by favorites."""
    email: str
    full_name: str
    favorite_count: int


class DetailedUserAnalytics(BaseModel):
    """Schema for detailed user analytics."""
    registration_trends: List[RegistrationTrend]
    top_users_by_favorites: List[TopUserByFavorites]


class ProductCreationTrend(BaseModel):
    """Schema for product creation trend."""
    date: str
    products_added: int


class TopCategory(BaseModel):
    """Schema for top category."""
    name: str
    product_count: int


class TopBrand(BaseModel):
    """Schema for top brand."""
    name: str
    product_count: int


class MostFavoritedProduct(BaseModel):
    """Schema for most favorited product."""
    title: str
    brand: Optional[str]
    favorite_count: int


class DetailedProductAnalytics(BaseModel):
    """Schema for detailed product analytics."""
    creation_trends: List[ProductCreationTrend]
    top_categories: List[TopCategory]
    top_brands: List[TopBrand]
    most_favorited_products: List[MostFavoritedProduct]


class DatabaseHealth(BaseModel):
    """Schema for database health."""
    status: str
    error: Optional[str]


class TableCounts(BaseModel):
    """Schema for table counts."""
    users: int
    products: int
    stores: int
    categories: int
    reviews: int
    scraping_jobs: int


class RecentActivityCounts(BaseModel):
    """Schema for recent activity counts."""
    users_last_24h: int
    products_last_24h: int
    scraping_jobs_last_24h: int


class SystemHealthResponse(BaseModel):
    """Schema for system health response."""
    database: DatabaseHealth
    table_counts: TableCounts
    recent_activity: RecentActivityCounts
    timestamp: str


class AdminUserListResponse(BaseModel):
    """Schema for admin user list response."""
    id: int
    email: str
    full_name: str
    role: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}


class UserManagementStats(BaseModel):
    """Schema for user management statistics."""
    total_users: int
    active_users: int
    inactive_users: int
    verified_users: int
    unverified_users: int
    users_by_role: Dict[str, int]
    recent_registrations: int


class ProductManagementStats(BaseModel):
    """Schema for product management statistics."""
    total_products: int
    active_products: int
    inactive_products: int
    featured_products: int
    available_products: int
    products_by_store: Dict[str, int]
    recent_additions: int


class ScrapingManagementStats(BaseModel):
    """Schema for scraping management statistics."""
    total_jobs: int
    pending_jobs: int
    running_jobs: int
    completed_jobs: int
    failed_jobs: int
    jobs_by_store: Dict[str, int]
    recent_jobs: int


class AdminStatsResponse(BaseModel):
    """Schema for comprehensive admin statistics."""
    user_stats: UserManagementStats
    product_stats: ProductManagementStats
    scraping_stats: ScrapingManagementStats
    system_health: SystemHealthResponse


class DataExportRequest(BaseModel):
    """Schema for data export request."""
    export_type: str = Field(..., description="Type of data to export")
    format: str = Field("csv", description="Export format")
    date_from: Optional[datetime] = Field(None, description="Start date for export")
    date_to: Optional[datetime] = Field(None, description="End date for export")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    
    @field_validator('export_type')
    @classmethod
    def validate_export_type(cls, v):
        """Validate export type."""
        allowed_types = ["users", "products", "reviews", "scraping_jobs", "analytics"]
        if v not in allowed_types:
            raise ValueError(f"Export type must be one of: {', '.join(allowed_types)}")
        return v
    
    @validator('format')
    def validate_format(cls, v):
        """Validate export format."""
        allowed_formats = ["csv", "json", "xlsx"]
        if v not in allowed_formats:
            raise ValueError(f"Format must be one of: {', '.join(allowed_formats)}")
        return v


class DataExportResponse(BaseModel):
    """Schema for data export response."""
    export_id: str
    export_type: str
    format: str
    status: str
    download_url: Optional[str]
    file_size: Optional[int]
    record_count: Optional[int]
    created_at: str
    expires_at: Optional[str]


class BulkActionRequest(BaseModel):
    """Schema for bulk action request."""
    action: str = Field(..., description="Action to perform")
    target_ids: List[int] = Field(..., min_items=1, max_items=1000, description="Target IDs")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Action parameters")
    
    @field_validator('action')
    @classmethod
    def validate_action(cls, v):
        """Validate bulk action."""
        allowed_actions = [
            "activate_users", "deactivate_users", "verify_users",
            "feature_products", "unfeature_products", "activate_products", "deactivate_products",
            "approve_reviews", "reject_reviews"
        ]
        if v not in allowed_actions:
            raise ValueError(f"Action must be one of: {', '.join(allowed_actions)}")
        return v


class BulkActionResponse(BaseModel):
    """Schema for bulk action response."""
    action: str
    total_targets: int
    successful_operations: int
    failed_operations: int
    errors: List[str]
    warnings: List[str]
    execution_time_seconds: float


class SystemConfigUpdate(BaseModel):
    """Schema for system configuration update."""
    scraping_enabled: Optional[bool] = None
    max_concurrent_scraping_jobs: Optional[int] = Field(None, ge=1, le=50)
    default_rate_limit: Optional[int] = Field(None, ge=10, le=10000)
    maintenance_mode: Optional[bool] = None
    registration_enabled: Optional[bool] = None
    email_verification_required: Optional[bool] = None


class SystemConfigResponse(BaseModel):
    """Schema for system configuration response."""
    scraping_enabled: bool
    max_concurrent_scraping_jobs: int
    default_rate_limit: int
    maintenance_mode: bool
    registration_enabled: bool
    email_verification_required: bool
    last_updated: str
    updated_by: str
