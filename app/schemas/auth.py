"""
Authentication-related Pydantic schemas.
"""
from typing import Optional
from pydantic import BaseModel, EmailStr, field_validator
from app.models.user import UserRole


class UserRegistration(BaseModel):
    """Schema for user registration."""
    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password strength."""
        from app.utils.security import validate_password_strength
        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {', '.join(errors)}")
        return v

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format."""
        return v.lower().strip()


class UserLogin(BaseModel):
    """Schema for user login."""
    email: EmailStr
    password: str
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format."""
        return v.lower().strip()


class TokenResponse(BaseModel):
    """Schema for token response."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenRefresh(BaseModel):
    """Schema for token refresh."""
    refresh_token: str


class PasswordReset(BaseModel):
    """Schema for password reset request."""
    email: EmailStr
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format."""
        return v.lower().strip()


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str
    new_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        """Validate password strength."""
        from app.utils.security import validate_password_strength
        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {', '.join(errors)}")
        return v


class EmailVerification(BaseModel):
    """Schema for email verification."""
    token: str


class ChangePassword(BaseModel):
    """Schema for changing password."""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        """Validate password strength."""
        from app.utils.security import validate_password_strength
        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {', '.join(errors)}")
        return v
