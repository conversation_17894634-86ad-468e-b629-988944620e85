"""
Admin service for administrative operations and analytics.
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.models.user import User, UserRole
from app.models.product import Product
from app.models.store import Store
from app.models.category import Category
from app.models.review import Review
from app.models.scraping_job import ScrapingJob, JobStatus
from app.models.user_favorite import UserFavorite
from app.schemas.user import UserRoleUpdate, UserStatusUpdate
from app.utils.exceptions import NotFoundException, AuthorizationException, ValidationException


class AdminService:
    """Service class for administrative operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_dashboard_stats(self, days: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive dashboard statistics.
        
        Args:
            days: Number of days to include in statistics
            
        Returns:
            Dict: Dashboard statistics
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # User statistics
        total_users = self.db.query(User).count()
        active_users = self.db.query(User).filter(User.is_active == True).count()
        verified_users = self.db.query(User).filter(User.is_verified == True).count()
        new_users = self.db.query(User).filter(User.created_at >= start_date).count()
        
        # User role distribution
        user_roles = (
            self.db.query(User.role, func.count(User.id))
            .group_by(User.role)
            .all()
        )
        role_distribution = {role.value: count for role, count in user_roles}
        
        # Product statistics
        total_products = self.db.query(Product).filter(Product.is_active == True).count()
        featured_products = self.db.query(Product).filter(
            and_(Product.is_active == True, Product.is_featured == True)
        ).count()
        available_products = self.db.query(Product).filter(
            and_(Product.is_active == True, Product.is_available == True)
        ).count()
        new_products = self.db.query(Product).filter(Product.created_at >= start_date).count()
        
        # Store statistics
        total_stores = self.db.query(Store).filter(Store.is_active == True).count()
        store_product_counts = (
            self.db.query(Store.display_name, func.count(Product.id))
            .join(Product, Store.id == Product.store_id)
            .filter(Product.is_active == True)
            .group_by(Store.id, Store.display_name)
            .all()
        )
        
        # Review statistics
        total_reviews = self.db.query(Review).count()
        approved_reviews = self.db.query(Review).filter(Review.is_approved == True).count()
        average_rating = self.db.query(func.avg(Review.rating)).scalar() or 0
        
        # Scraping statistics
        total_scraping_jobs = self.db.query(ScrapingJob).count()
        successful_jobs = self.db.query(ScrapingJob).filter(
            ScrapingJob.status == JobStatus.COMPLETED
        ).count()
        failed_jobs = self.db.query(ScrapingJob).filter(
            ScrapingJob.status == JobStatus.FAILED
        ).count()
        
        # Recent activity
        recent_users = (
            self.db.query(User)
            .filter(User.created_at >= start_date)
            .order_by(User.created_at.desc())
            .limit(10)
            .all()
        )
        
        recent_products = (
            self.db.query(Product)
            .filter(Product.created_at >= start_date)
            .order_by(Product.created_at.desc())
            .limit(10)
            .all()
        )
        
        return {
            "overview": {
                "total_users": total_users,
                "active_users": active_users,
                "verified_users": verified_users,
                "new_users": new_users,
                "total_products": total_products,
                "featured_products": featured_products,
                "available_products": available_products,
                "new_products": new_products,
                "total_stores": total_stores,
                "total_reviews": total_reviews,
                "approved_reviews": approved_reviews,
                "average_rating": round(float(average_rating), 2)
            },
            "user_analytics": {
                "role_distribution": role_distribution,
                "verification_rate": round((verified_users / total_users * 100), 2) if total_users > 0 else 0,
                "activation_rate": round((active_users / total_users * 100), 2) if total_users > 0 else 0
            },
            "product_analytics": {
                "availability_rate": round((available_products / total_products * 100), 2) if total_products > 0 else 0,
                "featured_rate": round((featured_products / total_products * 100), 2) if total_products > 0 else 0,
                "store_distribution": dict(store_product_counts)
            },
            "scraping_analytics": {
                "total_jobs": total_scraping_jobs,
                "success_rate": round((successful_jobs / total_scraping_jobs * 100), 2) if total_scraping_jobs > 0 else 0,
                "failure_rate": round((failed_jobs / total_scraping_jobs * 100), 2) if total_scraping_jobs > 0 else 0
            },
            "recent_activity": {
                "recent_users": [
                    {
                        "id": user.id,
                        "email": user.email,
                        "full_name": user.full_name,
                        "role": user.role.value,
                        "created_at": user.created_at.isoformat()
                    }
                    for user in recent_users
                ],
                "recent_products": [
                    {
                        "id": product.id,
                        "title": product.title,
                        "brand": product.brand,
                        "is_featured": product.is_featured,
                        "created_at": product.created_at.isoformat()
                    }
                    for product in recent_products
                ]
            }
        }
    
    def get_user_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get detailed user analytics."""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # User registration trends
        daily_registrations = (
            self.db.query(
                func.date(User.created_at).label('date'),
                func.count(User.id).label('count')
            )
            .filter(User.created_at >= start_date)
            .group_by(func.date(User.created_at))
            .order_by(func.date(User.created_at))
            .all()
        )
        
        # Top users by favorites
        top_users_by_favorites = (
            self.db.query(
                User.email,
                User.full_name,
                func.count(UserFavorite.id).label('favorite_count')
            )
            .join(UserFavorite, User.id == UserFavorite.user_id)
            .group_by(User.id, User.email, User.full_name)
            .order_by(func.count(UserFavorite.id).desc())
            .limit(10)
            .all()
        )
        
        return {
            "registration_trends": [
                {
                    "date": date.isoformat(),
                    "registrations": count
                }
                for date, count in daily_registrations
            ],
            "top_users_by_favorites": [
                {
                    "email": email,
                    "full_name": full_name,
                    "favorite_count": count
                }
                for email, full_name, count in top_users_by_favorites
            ]
        }
    
    def get_product_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get detailed product analytics."""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Product creation trends
        daily_products = (
            self.db.query(
                func.date(Product.created_at).label('date'),
                func.count(Product.id).label('count')
            )
            .filter(Product.created_at >= start_date)
            .group_by(func.date(Product.created_at))
            .order_by(func.date(Product.created_at))
            .all()
        )
        
        # Top categories by product count
        top_categories = (
            self.db.query(
                Category.name,
                func.count(Product.id).label('product_count')
            )
            .join(Product, Category.id == Product.category_id)
            .filter(Product.is_active == True)
            .group_by(Category.id, Category.name)
            .order_by(func.count(Product.id).desc())
            .limit(10)
            .all()
        )
        
        # Top brands by product count
        top_brands = (
            self.db.query(
                Product.brand,
                func.count(Product.id).label('product_count')
            )
            .filter(and_(Product.is_active == True, Product.brand.isnot(None)))
            .group_by(Product.brand)
            .order_by(func.count(Product.id).desc())
            .limit(10)
            .all()
        )
        
        # Most favorited products
        most_favorited = (
            self.db.query(
                Product.title,
                Product.brand,
                func.count(UserFavorite.id).label('favorite_count')
            )
            .join(UserFavorite, Product.id == UserFavorite.product_id)
            .group_by(Product.id, Product.title, Product.brand)
            .order_by(func.count(UserFavorite.id).desc())
            .limit(10)
            .all()
        )
        
        return {
            "creation_trends": [
                {
                    "date": date.isoformat(),
                    "products_added": count
                }
                for date, count in daily_products
            ],
            "top_categories": [
                {
                    "name": name,
                    "product_count": count
                }
                for name, count in top_categories
            ],
            "top_brands": [
                {
                    "name": brand,
                    "product_count": count
                }
                for brand, count in top_brands
            ],
            "most_favorited_products": [
                {
                    "title": title,
                    "brand": brand,
                    "favorite_count": count
                }
                for title, brand, count in most_favorited
            ]
        }
    
    def update_user_role(self, user_id: int, role_data: UserRoleUpdate, current_user: User) -> User:
        """Update user role (superadmin only)."""
        if not current_user.is_superadmin:
            raise AuthorizationException("Only superadmins can update user roles")
        
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise NotFoundException("User not found")
        
        # Prevent superadmin from demoting themselves
        if user.id == current_user.id and role_data.role != UserRole.SUPERADMIN:
            raise ValidationException("Cannot demote yourself from superadmin role")
        
        user.role = role_data.role
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def update_user_status(self, user_id: int, status_data: UserStatusUpdate, current_user: User) -> User:
        """Update user status (admin+ only)."""
        if not current_user.is_admin:
            raise AuthorizationException("Only admins can update user status")
        
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise NotFoundException("User not found")
        
        # Prevent admin from deactivating superadmin
        if user.is_superadmin and not current_user.is_superadmin:
            raise AuthorizationException("Cannot modify superadmin accounts")
        
        # Prevent user from deactivating themselves
        if user.id == current_user.id and not status_data.is_active:
            raise ValidationException("Cannot deactivate your own account")
        
        user.is_active = status_data.is_active
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get system health information."""
        # Database health
        try:
            self.db.execute("SELECT 1")
            db_status = "healthy"
            db_error = None
        except Exception as e:
            db_status = "unhealthy"
            db_error = str(e)
        
        # Table counts
        table_counts = {
            "users": self.db.query(User).count(),
            "products": self.db.query(Product).count(),
            "stores": self.db.query(Store).count(),
            "categories": self.db.query(Category).count(),
            "reviews": self.db.query(Review).count(),
            "scraping_jobs": self.db.query(ScrapingJob).count()
        }
        
        # Recent activity
        recent_activity = {
            "users_last_24h": self.db.query(User).filter(
                User.created_at >= datetime.utcnow() - timedelta(hours=24)
            ).count(),
            "products_last_24h": self.db.query(Product).filter(
                Product.created_at >= datetime.utcnow() - timedelta(hours=24)
            ).count(),
            "scraping_jobs_last_24h": self.db.query(ScrapingJob).filter(
                ScrapingJob.created_at >= datetime.utcnow() - timedelta(hours=24)
            ).count()
        }
        
        return {
            "database": {
                "status": db_status,
                "error": db_error
            },
            "table_counts": table_counts,
            "recent_activity": recent_activity,
            "timestamp": datetime.utcnow().isoformat()
        }
