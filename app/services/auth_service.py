"""
Authentication service for user management and authentication.
"""
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.user import User, UserRole
from app.schemas.auth import UserRegistration, UserLogin, TokenResponse
from app.schemas.user import UserCreate
from app.utils.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token,
    generate_verification_token,
    generate_reset_token
)
from app.utils.exceptions import (
    AuthenticationException,
    ConflictException,
    NotFoundException,
    ValidationException
)
from app.config.settings import get_settings

settings = get_settings()


class AuthService:
    """Authentication service class."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def register_user(self, user_data: UserRegistration) -> User:
        """
        Register a new user.
        
        Args:
            user_data: User registration data
            
        Returns:
            User: The created user
            
        Raises:
            ConflictException: If email already exists
        """
        # Check if user already exists
        existing_user = self.db.query(User).filter(User.email == user_data.email).first()
        if existing_user:
            raise ConflictException("Email already registered")
        
        # Create user
        user_create = UserCreate(
            email=user_data.email,
            password=user_data.password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            phone=user_data.phone,
            role=UserRole.USER
        )
        
        user = User(
            email=user_create.email,
            password_hash=get_password_hash(user_create.password),
            first_name=user_create.first_name,
            last_name=user_create.last_name,
            phone=user_create.phone,
            role=user_create.role,
            verification_token=generate_verification_token(),
            is_active=True,
            is_verified=False  # Require email verification
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        # TODO: Send verification email
        
        return user
    
    def authenticate_user(self, login_data: UserLogin) -> User:
        """
        Authenticate a user with email and password.
        
        Args:
            login_data: User login data
            
        Returns:
            User: The authenticated user
            
        Raises:
            AuthenticationException: If credentials are invalid
        """
        user = self.db.query(User).filter(User.email == login_data.email).first()
        
        if not user:
            raise AuthenticationException("Invalid email or password")
        
        if not verify_password(login_data.password, user.password_hash):
            raise AuthenticationException("Invalid email or password")
        
        if not user.is_active:
            raise AuthenticationException("Account is disabled")
        
        return user
    
    def create_tokens(self, user: User) -> TokenResponse:
        """
        Create access and refresh tokens for a user.
        
        Args:
            user: The user to create tokens for
            
        Returns:
            TokenResponse: The token response
        """
        access_token_expires = timedelta(minutes=settings.jwt_expire_minutes)
        
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role.value},
            expires_delta=access_token_expires
        )
        
        refresh_token = create_refresh_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.jwt_expire_minutes * 60
        )
    
    def refresh_token(self, refresh_token: str) -> TokenResponse:
        """
        Refresh an access token using a refresh token.
        
        Args:
            refresh_token: The refresh token
            
        Returns:
            TokenResponse: New token response
            
        Raises:
            AuthenticationException: If refresh token is invalid
        """
        payload = verify_token(refresh_token, "refresh")
        if payload is None:
            raise AuthenticationException("Invalid or expired refresh token")
        
        user_id = payload.get("sub")
        if user_id is None:
            raise AuthenticationException("Invalid token payload")
        
        user = self.db.query(User).filter(User.id == user_id).first()
        if user is None or not user.is_active:
            raise AuthenticationException("User not found or disabled")
        
        return self.create_tokens(user)
    
    def verify_email(self, token: str) -> User:
        """
        Verify a user's email address.
        
        Args:
            token: The verification token
            
        Returns:
            User: The verified user
            
        Raises:
            NotFoundException: If token is invalid
        """
        user = self.db.query(User).filter(User.verification_token == token).first()
        if not user:
            raise NotFoundException("Invalid verification token")
        
        user.is_verified = True
        user.verification_token = None
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def request_password_reset(self, email: str) -> bool:
        """
        Request a password reset for a user.
        
        Args:
            email: The user's email address
            
        Returns:
            bool: True if reset email was sent (or would be sent)
        """
        user = self.db.query(User).filter(User.email == email).first()
        if user and user.is_active:
            user.reset_token = generate_reset_token()
            self.db.commit()
            
            # TODO: Send password reset email
        
        # Always return True to prevent email enumeration
        return True
    
    def reset_password(self, token: str, new_password: str) -> User:
        """
        Reset a user's password using a reset token.
        
        Args:
            token: The reset token
            new_password: The new password
            
        Returns:
            User: The user with updated password
            
        Raises:
            NotFoundException: If token is invalid
        """
        user = self.db.query(User).filter(User.reset_token == token).first()
        if not user:
            raise NotFoundException("Invalid reset token")
        
        user.password_hash = get_password_hash(new_password)
        user.reset_token = None
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def change_password(self, user: User, current_password: str, new_password: str) -> User:
        """
        Change a user's password.
        
        Args:
            user: The user
            current_password: The current password
            new_password: The new password
            
        Returns:
            User: The user with updated password
            
        Raises:
            AuthenticationException: If current password is incorrect
        """
        if not verify_password(current_password, user.password_hash):
            raise AuthenticationException("Current password is incorrect")
        
        user.password_hash = get_password_hash(new_password)
        self.db.commit()
        self.db.refresh(user)
        
        return user
