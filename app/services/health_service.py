"""
Health check service for monitoring application status.
"""
import time
from typing import Dict, Any
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.config.settings import get_settings

settings = get_settings()


class HealthService:
    """Service for health checks and system monitoring."""
    
    def __init__(self):
        self.start_time = time.time()
    
    def get_basic_health(self) -> Dict[str, Any]:
        """Get basic health status."""
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": settings.app_version,
            "environment": settings.environment,
            "uptime_seconds": time.time() - self.start_time
        }
    
    def get_detailed_health(self, db: Session) -> Dict[str, Any]:
        """Get detailed health status including dependencies."""
        health_data = self.get_basic_health()
        
        # Database health
        health_data["database"] = self._check_database_health(db)
        
        # System resources
        health_data["system"] = self._get_system_info()
        
        # Application metrics
        health_data["metrics"] = self._get_app_metrics(db)
        
        # Overall status
        health_data["status"] = self._determine_overall_status(health_data)
        
        return health_data
    
    def _check_database_health(self, db: Session) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        try:
            start_time = time.time()
            
            # Simple query to test connectivity
            result = db.execute(text("SELECT 1"))
            result.fetchone()
            
            query_time = time.time() - start_time
            
            return {
                "status": "healthy",
                "response_time_ms": round(query_time * 1000, 2),
                "connection": "active"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "connection": "failed"
            }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system resource information."""
        if not HAS_PSUTIL:
            return {
                "error": "psutil not available - system monitoring disabled"
            }

        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory usage
            memory = psutil.virtual_memory()

            # Disk usage
            disk = psutil.disk_usage('/')

            return {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "percent_used": memory.percent
                },
                "disk": {
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "percent_used": round((disk.used / disk.total) * 100, 2)
                }
            }
        except Exception as e:
            return {
                "error": f"Failed to get system info: {str(e)}"
            }
    
    def _get_app_metrics(self, db: Session) -> Dict[str, Any]:
        """Get application-specific metrics."""
        try:
            metrics = {}
            
            # User counts
            from app.models.user import User
            total_users = db.query(User).count()
            active_users = db.query(User).filter(User.is_active == True).count()
            verified_users = db.query(User).filter(User.is_verified == True).count()
            
            metrics["users"] = {
                "total": total_users,
                "active": active_users,
                "verified": verified_users
            }
            
            # Product counts (if products table exists)
            try:
                from app.models.product import Product
                total_products = db.query(Product).count()
                active_products = db.query(Product).filter(Product.is_active == True).count()
                
                metrics["products"] = {
                    "total": total_products,
                    "active": active_products
                }
            except Exception:
                metrics["products"] = {"error": "Products table not accessible"}
            
            return metrics
        except Exception as e:
            return {
                "error": f"Failed to get app metrics: {str(e)}"
            }
    
    def _determine_overall_status(self, health_data: Dict[str, Any]) -> str:
        """Determine overall health status."""
        # Check database status
        if health_data.get("database", {}).get("status") != "healthy":
            return "unhealthy"
        
        # Check system resources
        system_info = health_data.get("system", {})
        if isinstance(system_info, dict) and "error" not in system_info:
            # Check if resources are critically low
            memory_percent = system_info.get("memory", {}).get("percent_used", 0)
            disk_percent = system_info.get("disk", {}).get("percent_used", 0)
            cpu_percent = system_info.get("cpu_percent", 0)
            
            if memory_percent > 90 or disk_percent > 95 or cpu_percent > 95:
                return "degraded"
        
        return "healthy"
    
    def get_readiness_check(self, db: Session) -> Dict[str, Any]:
        """Check if application is ready to serve requests."""
        try:
            # Test database connectivity
            db.execute(text("SELECT 1"))
            
            # Check if essential tables exist
            from app.models.user import User
            db.query(User).first()
            
            return {
                "status": "ready",
                "timestamp": datetime.utcnow().isoformat(),
                "checks": {
                    "database": "pass",
                    "models": "pass"
                }
            }
        except Exception as e:
            return {
                "status": "not_ready",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "checks": {
                    "database": "fail",
                    "models": "fail"
                }
            }
    
    def get_liveness_check(self) -> Dict[str, Any]:
        """Check if application is alive."""
        return {
            "status": "alive",
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": time.time() - self.start_time
        }


# Global health service instance
health_service = HealthService()
