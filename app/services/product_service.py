"""
Product service for product management operations.
"""
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, asc, func
try:
    from python_slugify import slugify
except ImportError:
    def slugify(text, max_length=100):
        """Simple fallback slugify function."""
        import re
        text = text.lower()
        text = re.sub(r'[^a-z0-9]+', '-', text)
        text = text.strip('-')
        return text[:max_length]

from app.models.product import Product, ProductPrice
from app.models.product_image import ProductImage
from app.models.store import Store
from app.models.category import Category
from app.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductSearchFilters,
    ProductPriceCreate,
    ProductImageCreate
)
from app.utils.exceptions import NotFoundException, ConflictException, ValidationException


class ProductService:
    """Service class for product management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_product(self, product_data: ProductCreate) -> Product:
        """
        Create a new product.
        
        Args:
            product_data: Product creation data
            
        Returns:
            Product: Created product
            
        Raises:
            NotFoundException: If store or category not found
            ConflictException: If product with same external_id exists
        """
        # Validate store exists
        store = self.db.query(Store).filter(Store.id == product_data.store_id).first()
        if not store:
            raise NotFoundException("Store not found")
        
        # Validate category exists (if provided)
        if product_data.category_id:
            category = self.db.query(Category).filter(Category.id == product_data.category_id).first()
            if not category:
                raise NotFoundException("Category not found")
        
        # Check for duplicate external_id within the same store
        if product_data.external_id:
            existing_product = (
                self.db.query(Product)
                .filter(
                    and_(
                        Product.store_id == product_data.store_id,
                        Product.external_id == product_data.external_id
                    )
                )
                .first()
            )
            if existing_product:
                raise ConflictException("Product with this external_id already exists in the store")
        
        # Create product
        product = Product(
            title=product_data.title,
            description=product_data.description,
            short_description=product_data.short_description,
            external_id=product_data.external_id,
            asin=product_data.asin,
            sku=product_data.sku,
            store_id=product_data.store_id,
            category_id=product_data.category_id,
            brand=product_data.brand,
            model=product_data.model,
            manufacturer=product_data.manufacturer,
            is_available=product_data.is_available,
            stock_quantity=product_data.stock_quantity,
            availability_status=product_data.availability_status,
            shipping_weight=product_data.shipping_weight,
            shipping_dimensions=product_data.shipping_dimensions,
            free_shipping=product_data.free_shipping,
            product_url=product_data.product_url,
            is_featured=product_data.is_featured,
            slug=self._generate_slug(product_data.title),
            is_active=True
        )
        
        self.db.add(product)
        self.db.flush()  # Get the product ID
        
        # Add initial price if provided
        if product_data.initial_price:
            self._create_product_price(product.id, product_data.initial_price)
        
        # Add initial images if provided
        if product_data.initial_images:
            for image_data in product_data.initial_images:
                self._create_product_image(product.id, image_data)
        
        self.db.commit()
        self.db.refresh(product)
        
        return product
    
    def get_product_by_id(self, product_id: int, include_relations: bool = True) -> Optional[Product]:
        """
        Get product by ID.
        
        Args:
            product_id: Product ID
            include_relations: Whether to include related data
            
        Returns:
            Product: Product object or None if not found
        """
        query = self.db.query(Product).filter(Product.id == product_id)
        
        if include_relations:
            query = query.options(
                joinedload(Product.store),
                joinedload(Product.category),
                joinedload(Product.prices),
                joinedload(Product.images)
            )
        
        return query.first()
    
    def update_product(self, product_id: int, product_data: ProductUpdate) -> Product:
        """
        Update a product.
        
        Args:
            product_id: Product ID
            product_data: Update data
            
        Returns:
            Product: Updated product
            
        Raises:
            NotFoundException: If product not found
        """
        product = self.get_product_by_id(product_id, include_relations=False)
        if not product:
            raise NotFoundException("Product not found")
        
        # Update fields
        update_data = product_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            if field == "category_id" and value is not None:
                # Validate category exists
                category = self.db.query(Category).filter(Category.id == value).first()
                if not category:
                    raise NotFoundException("Category not found")
            
            setattr(product, field, value)
        
        # Update slug if title changed
        if "title" in update_data:
            product.slug = self._generate_slug(product.title)
        
        self.db.commit()
        self.db.refresh(product)
        
        return product
    
    def delete_product(self, product_id: int) -> bool:
        """
        Delete a product (soft delete).
        
        Args:
            product_id: Product ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        product = self.get_product_by_id(product_id, include_relations=False)
        if not product:
            return False
        
        product.is_active = False
        self.db.commit()
        
        return True
    
    def search_products(
        self,
        filters: Optional[ProductSearchFilters] = None,
        skip: int = 0,
        limit: int = 50,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Tuple[List[Product], int]:
        """
        Search products with filters, pagination, and sorting.
        
        Args:
            filters: Search filters
            skip: Number of items to skip
            limit: Maximum number of items to return
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            
        Returns:
            Tuple[List[Product], int]: (products, total_count)
        """
        query = self.db.query(Product).filter(Product.is_active == True)
        
        # Apply filters
        if filters:
            query = self._apply_filters(query, filters)
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply sorting
        query = self._apply_sorting(query, sort_by, sort_order)
        
        # Apply pagination
        products = query.offset(skip).limit(limit).all()
        
        return products, total_count
    
    def _apply_filters(self, query, filters: ProductSearchFilters):
        """Apply search filters to query."""
        if filters.query:
            search_term = f"%{filters.query}%"
            query = query.filter(
                or_(
                    Product.title.ilike(search_term),
                    Product.description.ilike(search_term),
                    Product.brand.ilike(search_term)
                )
            )
        
        if filters.category_id:
            query = query.filter(Product.category_id == filters.category_id)
        
        if filters.store_id:
            query = query.filter(Product.store_id == filters.store_id)
        
        if filters.brand:
            query = query.filter(Product.brand.ilike(f"%{filters.brand}%"))
        
        if filters.is_available is not None:
            query = query.filter(Product.is_available == filters.is_available)
        
        if filters.is_featured is not None:
            query = query.filter(Product.is_featured == filters.is_featured)
        
        if filters.free_shipping is not None:
            query = query.filter(Product.free_shipping == filters.free_shipping)
        
        # Price filters (requires joining with ProductPrice)
        if filters.min_price or filters.max_price:
            query = query.join(ProductPrice)
            
            if filters.min_price:
                query = query.filter(ProductPrice.current_price >= filters.min_price)
            
            if filters.max_price:
                query = query.filter(ProductPrice.current_price <= filters.max_price)
        
        # Rating filter (requires aggregating reviews)
        if filters.min_rating:
            from app.models.review import Review
            subquery = (
                self.db.query(
                    Review.product_id,
                    func.avg(Review.rating).label('avg_rating')
                )
                .group_by(Review.product_id)
                .having(func.avg(Review.rating) >= filters.min_rating)
                .subquery()
            )
            query = query.join(subquery, Product.id == subquery.c.product_id)
        
        return query
    
    def _apply_sorting(self, query, sort_by: str, sort_order: str):
        """Apply sorting to query."""
        # Map sort fields to actual model attributes
        sort_fields = {
            "created_at": Product.created_at,
            "updated_at": Product.updated_at,
            "title": Product.title,
            "brand": Product.brand,
            "price": ProductPrice.current_price,  # Requires join
            "rating": func.avg(func.coalesce(func.avg(func.coalesce(Product.id, 0)), 0))  # Placeholder
        }
        
        if sort_by not in sort_fields:
            sort_by = "created_at"
        
        sort_field = sort_fields[sort_by]
        
        if sort_order == "asc":
            query = query.order_by(asc(sort_field))
        else:
            query = query.order_by(desc(sort_field))
        
        return query
    
    def _generate_slug(self, title: str) -> str:
        """Generate URL-friendly slug from title."""
        return slugify(title, max_length=100)
    
    def _create_product_price(self, product_id: int, price_data: ProductPriceCreate) -> ProductPrice:
        """Create a product price record."""
        price = ProductPrice(
            product_id=product_id,
            current_price=price_data.current_price,
            original_price=price_data.original_price,
            currency=price_data.currency,
            source=price_data.source
        )
        
        self.db.add(price)
        return price
    
    def _create_product_image(self, product_id: int, image_data: ProductImageCreate) -> ProductImage:
        """Create a product image record."""
        image = ProductImage(
            product_id=product_id,
            image_url=image_data.image_url,
            alt_text=image_data.alt_text,
            title=image_data.title,
            is_primary=image_data.is_primary,
            sort_order=image_data.sort_order,
            source="manual"
        )
        
        self.db.add(image)
        return image
