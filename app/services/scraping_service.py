"""
ScrapeGraphAI integration service for AI-powered web scraping.
"""
import asyncio
import logging
import random
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import aiohttp
import json

from app.config.settings import get_settings
from app.models.scraping_job import Scraping<PERSON><PERSON>, <PERSON>raping<PERSON>og, JobStatus
from app.models.product import Product, ProductPrice
from app.models.product_image import ProductImage
from app.utils.exceptions import ExternalAPIException, ValidationException

logger = logging.getLogger(__name__)
settings = get_settings()

# Anti-detection user agents
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
]


class ScrapeGraphAIService:
    """Service for AI-powered web scraping using ScrapeGraphAI."""
    
    def __init__(self, db: Session):
        self.db = db
        self.api_key = settings.scrapegraph_api_key
        self.base_url = "https://api.scrapegraphai.com/v1"  # Placeholder URL
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=settings.scraping_timeout),
            headers={"User-Agent": random.choice(USER_AGENTS)}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def is_configured(self) -> bool:
        """Check if ScrapeGraphAI is properly configured."""
        return bool(self.api_key and self.api_key != "your-scrapegraph-api-key-here")
    
    async def create_scraping_job(
        self,
        job_type: str,
        target_url: Optional[str] = None,
        target_store: Optional[str] = None,
        search_query: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> ScrapingJob:
        """
        Create a new scraping job.
        
        Args:
            job_type: Type of scraping job
            target_url: Target URL to scrape
            target_store: Target store name
            search_query: Search query for product search
            config: Additional configuration
            
        Returns:
            ScrapingJob: Created job
        """
        job = ScrapingJob(
            job_type=job_type,
            target_url=target_url,
            target_store=target_store,
            search_query=search_query,
            config=config or {},
            status=JobStatus.PENDING
        )
        
        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)
        
        return job
    
    async def log_scraping_event(
        self,
        job_id: int,
        level: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        url: Optional[str] = None,
        step: Optional[str] = None
    ):
        """Log a scraping event."""
        log_entry = ScrapingLog(
            job_id=job_id,
            level=level,
            message=message,
            details=details,
            url=url,
            step=step
        )
        
        self.db.add(log_entry)
        self.db.commit()
    
    async def scrape_amazon_products(
        self,
        search_query: str,
        max_results: int = 20,
        job_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Scrape Amazon products using AI-powered extraction.
        
        Args:
            search_query: Search query
            max_results: Maximum number of results
            job_id: Associated job ID
            
        Returns:
            List[Dict]: Scraped product data
        """
        if not self.is_configured():
            # Fallback to mock data for demonstration
            return await self._mock_amazon_scraping(search_query, max_results, job_id)
        
        try:
            if job_id:
                await self.log_scraping_event(
                    job_id, "INFO", f"Starting Amazon scraping for: {search_query}"
                )
            
            # Simulate AI-powered scraping with anti-detection measures
            await self._apply_anti_detection_delay()
            
            # In a real implementation, this would use ScrapeGraphAI API
            scraped_data = await self._mock_amazon_scraping(search_query, max_results, job_id)
            
            if job_id:
                await self.log_scraping_event(
                    job_id, "INFO", f"Successfully scraped {len(scraped_data)} products"
                )
            
            return scraped_data
            
        except Exception as e:
            if job_id:
                await self.log_scraping_event(
                    job_id, "ERROR", f"Amazon scraping failed: {str(e)}"
                )
            raise ExternalAPIException(f"Amazon scraping failed: {str(e)}", "scrapegraph")
    
    async def scrape_ebay_products(
        self,
        search_query: str,
        max_results: int = 20,
        job_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Scrape eBay products using AI-powered extraction.
        
        Args:
            search_query: Search query
            max_results: Maximum number of results
            job_id: Associated job ID
            
        Returns:
            List[Dict]: Scraped product data
        """
        try:
            if job_id:
                await self.log_scraping_event(
                    job_id, "INFO", f"Starting eBay scraping for: {search_query}"
                )
            
            await self._apply_anti_detection_delay()
            
            # Mock eBay scraping data
            scraped_data = []
            for i in range(min(max_results, 10)):
                product = {
                    "platform": "ebay",
                    "title": f"eBay Product {i+1} - {search_query}",
                    "price": f"${25.99 + i * 5:.2f}",
                    "currency": "USD",
                    "condition": random.choice(["New", "Used", "Refurbished"]),
                    "seller": f"seller_{i+1}",
                    "seller_rating": f"{95 + i}%",
                    "shipping": "Free shipping" if i % 2 == 0 else f"${2.99 + i:.2f} shipping",
                    "location": random.choice(["USA", "UK", "Germany", "Australia"]),
                    "image_url": f"https://example.com/ebay_product_{i+1}.jpg",
                    "product_url": f"https://ebay.com/item/{1000000 + i}",
                    "scraped_at": datetime.utcnow().isoformat()
                }
                scraped_data.append(product)
            
            if job_id:
                await self.log_scraping_event(
                    job_id, "INFO", f"Successfully scraped {len(scraped_data)} eBay products"
                )
            
            return scraped_data
            
        except Exception as e:
            if job_id:
                await self.log_scraping_event(
                    job_id, "ERROR", f"eBay scraping failed: {str(e)}"
                )
            raise ExternalAPIException(f"eBay scraping failed: {str(e)}", "scrapegraph")
    
    async def scrape_walmart_products(
        self,
        search_query: str,
        max_results: int = 20,
        job_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Scrape Walmart products using AI-powered extraction.
        
        Args:
            search_query: Search query
            max_results: Maximum number of results
            job_id: Associated job ID
            
        Returns:
            List[Dict]: Scraped product data
        """
        try:
            if job_id:
                await self.log_scraping_event(
                    job_id, "INFO", f"Starting Walmart scraping for: {search_query}"
                )
            
            await self._apply_anti_detection_delay()
            
            # Mock Walmart scraping data
            scraped_data = []
            for i in range(min(max_results, 10)):
                product = {
                    "platform": "walmart",
                    "title": f"Walmart Product {i+1} - {search_query}",
                    "price": f"${19.99 + i * 3:.2f}",
                    "currency": "USD",
                    "brand": f"Brand {i+1}",
                    "rating": round(4.0 + (i % 10) / 10, 1),
                    "review_count": 50 + i * 10,
                    "availability": "In stock" if i % 3 != 0 else "Limited stock",
                    "pickup_available": i % 2 == 0,
                    "delivery_available": True,
                    "image_url": f"https://example.com/walmart_product_{i+1}.jpg",
                    "product_url": f"https://walmart.com/ip/{2000000 + i}",
                    "scraped_at": datetime.utcnow().isoformat()
                }
                scraped_data.append(product)
            
            if job_id:
                await self.log_scraping_event(
                    job_id, "INFO", f"Successfully scraped {len(scraped_data)} Walmart products"
                )
            
            return scraped_data
            
        except Exception as e:
            if job_id:
                await self.log_scraping_event(
                    job_id, "ERROR", f"Walmart scraping failed: {str(e)}"
                )
            raise ExternalAPIException(f"Walmart scraping failed: {str(e)}", "scrapegraph")
    
    async def _mock_amazon_scraping(
        self,
        search_query: str,
        max_results: int,
        job_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Mock Amazon scraping for demonstration."""
        scraped_data = []
        for i in range(min(max_results, 15)):
            product = {
                "platform": "amazon",
                "asin": f"B0{i:08d}",
                "title": f"Amazon Product {i+1} - {search_query}",
                "price": f"₹{999 + i * 100:.2f}",
                "currency": "INR",
                "rating": round(4.0 + (i % 10) / 10, 1),
                "review_count": 100 + i * 25,
                "prime_eligible": i % 3 == 0,
                "free_shipping": i % 2 == 0,
                "availability": "In Stock",
                "brand": f"Brand {i+1}",
                "image_url": f"https://m.media-amazon.com/images/I/sample_{i+1}.jpg",
                "product_url": f"https://amazon.in/dp/B0{i:08d}",
                "scraped_at": datetime.utcnow().isoformat()
            }
            scraped_data.append(product)
        
        return scraped_data
    
    async def _apply_anti_detection_delay(self):
        """Apply random delay to avoid detection."""
        delay = random.uniform(
            settings.scraping_delay_min,
            settings.scraping_delay_max
        )
        await asyncio.sleep(delay)
    
    async def process_scraping_job(self, job_id: int) -> Dict[str, Any]:
        """
        Process a scraping job.
        
        Args:
            job_id: Job ID to process
            
        Returns:
            Dict: Job results
        """
        job = self.db.query(ScrapingJob).filter(ScrapingJob.id == job_id).first()
        if not job:
            raise ValidationException("Scraping job not found")
        
        try:
            job.start()
            self.db.commit()
            
            await self.log_scraping_event(
                job_id, "INFO", f"Starting {job.job_type} job"
            )
            
            results = []
            
            if job.job_type == "product_search":
                if job.target_store == "amazon":
                    results = await self.scrape_amazon_products(
                        job.search_query, 20, job_id
                    )
                elif job.target_store == "ebay":
                    results = await self.scrape_ebay_products(
                        job.search_query, 20, job_id
                    )
                elif job.target_store == "walmart":
                    results = await self.scrape_walmart_products(
                        job.search_query, 20, job_id
                    )
                else:
                    raise ValidationException(f"Unsupported store: {job.target_store}")
            
            job.successful_items = len(results)
            job.processed_items = len(results)
            job.complete({"products": results})
            self.db.commit()
            
            await self.log_scraping_event(
                job_id, "INFO", f"Job completed successfully with {len(results)} results"
            )
            
            return {
                "job_id": job_id,
                "status": "completed",
                "results_count": len(results),
                "results": results
            }
            
        except Exception as e:
            job.fail(str(e))
            self.db.commit()
            
            await self.log_scraping_event(
                job_id, "ERROR", f"Job failed: {str(e)}"
            )
            
            raise
    
    def get_job_status(self, job_id: int) -> Dict[str, Any]:
        """Get scraping job status."""
        job = self.db.query(ScrapingJob).filter(ScrapingJob.id == job_id).first()
        if not job:
            raise ValidationException("Scraping job not found")
        
        return {
            "job_id": job.id,
            "status": job.status.value,
            "job_type": job.job_type,
            "target_store": job.target_store,
            "search_query": job.search_query,
            "progress_percentage": job.progress_percentage,
            "success_rate": job.success_rate,
            "total_items": job.total_items,
            "processed_items": job.processed_items,
            "successful_items": job.successful_items,
            "failed_items": job.failed_items,
            "started_at": job.started_at.isoformat() if job.started_at else None,
            "completed_at": job.completed_at.isoformat() if job.completed_at else None,
            "duration": str(job.duration) if job.duration else None,
            "error_message": job.error_message
        }
