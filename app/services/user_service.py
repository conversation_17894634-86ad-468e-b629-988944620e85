"""
User service for user management operations.
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.user import User, UserRole
from app.models.product import Product
from app.models.user_favorite import UserFavorite
from app.schemas.user import User<PERSON>pdate, UserCreate, UserRoleUpdate
from app.utils.exceptions import NotFoundException, ConflictException, AuthorizationException
from app.utils.security import get_password_hash


class UserService:
    """Service class for user management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            User: User object or None if not found
        """
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email.
        
        Args:
            email: User email
            
        Returns:
            User: User object or None if not found
        """
        return self.db.query(User).filter(User.email == email.lower()).first()
    
    def create_user(self, user_data: UserCreate) -> User:
        """
        Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            User: Created user
            
        Raises:
            ConflictException: If email already exists
        """
        # Check if user already exists
        existing_user = self.get_user_by_email(user_data.email)
        if existing_user:
            raise ConflictException("Email already registered")
        
        # Create user
        user = User(
            email=user_data.email.lower(),
            password_hash=get_password_hash(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            phone=user_data.phone,
            address=user_data.address,
            city=user_data.city,
            country=user_data.country,
            postal_code=user_data.postal_code,
            role=user_data.role,
            is_active=True,
            is_verified=False
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def update_user(self, user: User, user_data: UserUpdate) -> User:
        """
        Update user information.
        
        Args:
            user: User to update
            user_data: Update data
            
        Returns:
            User: Updated user
        """
        update_data = user_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def update_user_role(self, user_id: int, role_data: UserRoleUpdate, current_user: User) -> User:
        """
        Update user role (superadmin only).
        
        Args:
            user_id: ID of user to update
            role_data: New role data
            current_user: User making the request
            
        Returns:
            User: Updated user
            
        Raises:
            AuthorizationException: If not superadmin
            NotFoundException: If user not found
        """
        if not current_user.is_superadmin:
            raise AuthorizationException("Only superadmins can update user roles")
        
        user = self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("User not found")
        
        user.role = role_data.role
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def deactivate_user(self, user_id: int, current_user: User) -> User:
        """
        Deactivate a user account.
        
        Args:
            user_id: ID of user to deactivate
            current_user: User making the request
            
        Returns:
            User: Deactivated user
            
        Raises:
            AuthorizationException: If not admin or superadmin
            NotFoundException: If user not found
        """
        if not current_user.is_admin:
            raise AuthorizationException("Only admins can deactivate users")
        
        user = self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("User not found")
        
        user.is_active = False
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def activate_user(self, user_id: int, current_user: User) -> User:
        """
        Activate a user account.
        
        Args:
            user_id: ID of user to activate
            current_user: User making the request
            
        Returns:
            User: Activated user
            
        Raises:
            AuthorizationException: If not admin or superadmin
            NotFoundException: If user not found
        """
        if not current_user.is_admin:
            raise AuthorizationException("Only admins can activate users")
        
        user = self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("User not found")
        
        user.is_active = True
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def get_users(
        self,
        skip: int = 0,
        limit: int = 50,
        role: Optional[UserRole] = None,
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None
    ) -> List[User]:
        """
        Get list of users with filtering.
        
        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return
            role: Filter by role
            is_active: Filter by active status
            is_verified: Filter by verified status
            
        Returns:
            List[User]: List of users
        """
        query = self.db.query(User)
        
        if role is not None:
            query = query.filter(User.role == role)
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        if is_verified is not None:
            query = query.filter(User.is_verified == is_verified)
        
        return query.offset(skip).limit(limit).all()
    
    def get_user_favorites(self, user: User, skip: int = 0, limit: int = 50) -> List[Product]:
        """
        Get user's favorite products.
        
        Args:
            user: User object
            skip: Number of items to skip
            limit: Maximum number of items to return
            
        Returns:
            List[Product]: List of favorite products
        """
        return (
            self.db.query(Product)
            .join(UserFavorite)
            .filter(UserFavorite.user_id == user.id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def add_to_favorites(self, user: User, product_id: int) -> UserFavorite:
        """
        Add product to user's favorites.
        
        Args:
            user: User object
            product_id: Product ID
            
        Returns:
            UserFavorite: Created favorite
            
        Raises:
            NotFoundException: If product not found
            ConflictException: If already in favorites
        """
        # Check if product exists
        product = self.db.query(Product).filter(Product.id == product_id).first()
        if not product:
            raise NotFoundException("Product not found")
        
        # Check if already in favorites
        existing_favorite = (
            self.db.query(UserFavorite)
            .filter(
                and_(
                    UserFavorite.user_id == user.id,
                    UserFavorite.product_id == product_id
                )
            )
            .first()
        )
        
        if existing_favorite:
            raise ConflictException("Product is already in favorites")
        
        # Add to favorites
        favorite = UserFavorite(user_id=user.id, product_id=product_id)
        self.db.add(favorite)
        self.db.commit()
        self.db.refresh(favorite)
        
        return favorite
    
    def remove_from_favorites(self, user: User, product_id: int) -> bool:
        """
        Remove product from user's favorites.
        
        Args:
            user: User object
            product_id: Product ID
            
        Returns:
            bool: True if removed, False if not found
        """
        favorite = (
            self.db.query(UserFavorite)
            .filter(
                and_(
                    UserFavorite.user_id == user.id,
                    UserFavorite.product_id == product_id
                )
            )
            .first()
        )
        
        if favorite:
            self.db.delete(favorite)
            self.db.commit()
            return True
        
        return False
    
    def is_product_favorite(self, user: User, product_id: int) -> bool:
        """
        Check if product is in user's favorites.
        
        Args:
            user: User object
            product_id: Product ID
            
        Returns:
            bool: True if in favorites, False otherwise
        """
        favorite = (
            self.db.query(UserFavorite)
            .filter(
                and_(
                    UserFavorite.user_id == user.id,
                    UserFavorite.product_id == product_id
                )
            )
            .first()
        )
        
        return favorite is not None
