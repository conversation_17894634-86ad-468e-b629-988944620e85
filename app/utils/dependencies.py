"""
FastAPI dependencies for authentication and authorization.
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.models.user import User, UserRole
from app.utils.security import verify_token
from app.utils.exceptions import AuthenticationException, AuthorizationException

# HTTP Bearer token scheme
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current authenticated user from JWT token.
    
    Args:
        credentials: HTTP Bearer credentials
        db: Database session
        
    Returns:
        User: The authenticated user
        
    Raises:
        AuthenticationException: If token is invalid or user not found
    """
    token = credentials.credentials
    payload = verify_token(token, "access")
    
    if payload is None:
        raise AuthenticationException("Invalid or expired token")
    
    user_id = payload.get("sub")
    if user_id is None:
        raise AuthenticationException("Invalid token payload")
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise AuthenticationException("User not found")
    
    if not user.is_active:
        raise AuthenticationException("User account is disabled")
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current active user.
    
    Args:
        current_user: The current user from token
        
    Returns:
        User: The active user
        
    Raises:
        AuthenticationException: If user is not active
    """
    if not current_user.is_active:
        raise AuthenticationException("User account is disabled")
    
    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Get the current verified user.
    
    Args:
        current_user: The current active user
        
    Returns:
        User: The verified user
        
    Raises:
        AuthenticationException: If user is not verified
    """
    if not current_user.is_verified:
        raise AuthenticationException("User account is not verified")
    
    return current_user


def require_role(required_role: UserRole):
    """
    Create a dependency that requires a specific user role.
    
    Args:
        required_role: The minimum required role
        
    Returns:
        function: A dependency function
    """
    async def role_checker(
        current_user: User = Depends(get_current_verified_user)
    ) -> User:
        if not current_user.has_permission(required_role):
            raise AuthorizationException(
                f"Insufficient permissions. Required: {required_role.value}"
            )
        return current_user
    
    return role_checker


# Pre-defined role dependencies
require_user = require_role(UserRole.USER)
require_admin = require_role(UserRole.ADMIN)
require_superadmin = require_role(UserRole.SUPERADMIN)


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get the current user if authenticated, otherwise return None.
    Used for endpoints that work for both authenticated and anonymous users.
    
    Args:
        credentials: Optional HTTP Bearer credentials
        db: Database session
        
    Returns:
        Optional[User]: The authenticated user or None
    """
    if credentials is None:
        return None
    
    try:
        token = credentials.credentials
        payload = verify_token(token, "access")
        
        if payload is None:
            return None
        
        user_id = payload.get("sub")
        if user_id is None:
            return None
        
        user = db.query(User).filter(User.id == user_id).first()
        if user is None or not user.is_active:
            return None
        
        return user
    except Exception:
        return None


class RoleChecker:
    """Class-based role checker for more complex authorization logic."""
    
    def __init__(self, allowed_roles: list[UserRole]):
        self.allowed_roles = allowed_roles
    
    def __call__(self, current_user: User = Depends(get_current_verified_user)) -> User:
        if current_user.role not in self.allowed_roles:
            raise AuthorizationException(
                f"Access denied. Allowed roles: {[role.value for role in self.allowed_roles]}"
            )
        return current_user


# Common role combinations
admin_or_superadmin = RoleChecker([UserRole.ADMIN, UserRole.SUPERADMIN])
all_roles = RoleChecker([UserRole.USER, UserRole.ADMIN, UserRole.SUPERADMIN])
