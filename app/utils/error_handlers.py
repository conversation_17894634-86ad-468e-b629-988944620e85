"""
Centralized error handling utilities.
"""
import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError
import logging

from app.utils.exceptions import CustomHTTPException

logger = logging.getLogger(__name__)


def create_error_response(
    status_code: int,
    error_type: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    Create a standardized error response.
    
    Args:
        status_code: HTTP status code
        error_type: Type of error
        message: Error message
        details: Additional error details
        request_id: Request ID for tracking
        
    Returns:
        JSONResponse: Standardized error response
    """
    error_data = {
        "error": {
            "type": error_type,
            "message": message,
            "status_code": status_code
        }
    }
    
    if details:
        error_data["error"]["details"] = details
    
    if request_id:
        error_data["error"]["request_id"] = request_id
    
    return JSONResponse(
        status_code=status_code,
        content=error_data
    )


def handle_database_error(error: SQLAlchemyError, request: Request) -> JSONResponse:
    """
    Handle database-related errors.
    
    Args:
        error: SQLAlchemy error
        request: FastAPI request object
        
    Returns:
        JSONResponse: Error response
    """
    logger.error(f"Database error: {error}", exc_info=True)
    
    if isinstance(error, IntegrityError):
        # Handle constraint violations
        if "UNIQUE constraint failed" in str(error):
            return create_error_response(
                status_code=409,
                error_type="conflict",
                message="Resource already exists",
                details={"constraint": "unique_violation"}
            )
        elif "FOREIGN KEY constraint failed" in str(error):
            return create_error_response(
                status_code=400,
                error_type="validation_error",
                message="Invalid reference to related resource",
                details={"constraint": "foreign_key_violation"}
            )
    
    # Generic database error
    return create_error_response(
        status_code=500,
        error_type="database_error",
        message="A database error occurred"
    )


def handle_validation_error(error: ValidationError, request: Request) -> JSONResponse:
    """
    Handle Pydantic validation errors.
    
    Args:
        error: Pydantic validation error
        request: FastAPI request object
        
    Returns:
        JSONResponse: Error response
    """
    logger.warning(f"Validation error: {error}")
    
    # Format validation errors
    formatted_errors = []
    for err in error.errors():
        formatted_errors.append({
            "field": ".".join(str(x) for x in err["loc"]),
            "message": err["msg"],
            "type": err["type"]
        })
    
    return create_error_response(
        status_code=422,
        error_type="validation_error",
        message="Request validation failed",
        details={"errors": formatted_errors}
    )


def handle_http_exception(error: HTTPException, request: Request) -> JSONResponse:
    """
    Handle FastAPI HTTP exceptions.
    
    Args:
        error: HTTP exception
        request: FastAPI request object
        
    Returns:
        JSONResponse: Error response
    """
    logger.warning(f"HTTP exception: {error.status_code} - {error.detail}")
    
    error_type = "http_error"
    if error.status_code == 401:
        error_type = "authentication_error"
    elif error.status_code == 403:
        error_type = "authorization_error"
    elif error.status_code == 404:
        error_type = "not_found"
    elif error.status_code == 429:
        error_type = "rate_limit_exceeded"
    
    return create_error_response(
        status_code=error.status_code,
        error_type=error_type,
        message=error.detail
    )


def handle_custom_exception(error: CustomHTTPException, request: Request) -> JSONResponse:
    """
    Handle custom HTTP exceptions.
    
    Args:
        error: Custom HTTP exception
        request: FastAPI request object
        
    Returns:
        JSONResponse: Error response
    """
    logger.warning(f"Custom exception: {error.status_code} - {error.detail}")
    
    return create_error_response(
        status_code=error.status_code,
        error_type=error.error_type,
        message=error.detail
    )


def handle_generic_exception(error: Exception, request: Request) -> JSONResponse:
    """
    Handle generic exceptions.
    
    Args:
        error: Generic exception
        request: FastAPI request object
        
    Returns:
        JSONResponse: Error response
    """
    logger.error(f"Unhandled exception: {error}", exc_info=True)
    
    # In production, don't expose internal error details
    from app.config.settings import get_settings
    settings = get_settings()
    
    if settings.debug:
        details = {
            "exception_type": type(error).__name__,
            "traceback": traceback.format_exc()
        }
    else:
        details = None
    
    return create_error_response(
        status_code=500,
        error_type="internal_server_error",
        message="An internal server error occurred",
        details=details
    )


class ErrorTracker:
    """Track and analyze application errors."""
    
    def __init__(self):
        self.error_counts = {}
        self.recent_errors = []
        self.max_recent_errors = 100
    
    def track_error(
        self,
        error_type: str,
        message: str,
        status_code: int,
        request_path: str,
        user_id: Optional[int] = None
    ):
        """Track an error occurrence."""
        # Update error counts
        key = f"{error_type}:{status_code}"
        self.error_counts[key] = self.error_counts.get(key, 0) + 1
        
        # Add to recent errors
        error_info = {
            "type": error_type,
            "message": message,
            "status_code": status_code,
            "path": request_path,
            "user_id": user_id,
            "timestamp": logger.handlers[0].formatter.formatTime(
                logging.LogRecord("", 0, "", 0, "", (), None)
            )
        }
        
        self.recent_errors.append(error_info)
        
        # Keep only recent errors
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors = self.recent_errors[-self.max_recent_errors:]
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return {
            "error_counts": self.error_counts,
            "recent_errors": self.recent_errors[-10:],  # Last 10 errors
            "total_tracked_errors": sum(self.error_counts.values())
        }


# Global error tracker instance
error_tracker = ErrorTracker()
