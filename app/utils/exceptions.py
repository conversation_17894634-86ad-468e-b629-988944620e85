"""
Custom exception classes for the application.
"""
from typing import Any, Dict, List, Optional
from fastapi import HTTPException, status


class CustomHTTPException(HTTPException):
    """Base custom HTTP exception with additional error type."""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_type: str = "error",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_type = error_type


class ValidationException(CustomHTTPException):
    """Exception for validation errors."""
    
    def __init__(
        self,
        detail: str,
        errors: Optional[List[Dict[str, Any]]] = None,
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_type="validation_error",
            headers=headers
        )
        self.errors = errors or []


class AuthenticationException(CustomHTTPException):
    """Exception for authentication errors."""
    
    def __init__(
        self,
        detail: str = "Authentication failed",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_type="authentication_error",
            headers=headers or {"WWW-Authenticate": "Bearer"}
        )


class AuthorizationException(CustomHTTPException):
    """Exception for authorization errors."""
    
    def __init__(
        self,
        detail: str = "Insufficient permissions",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_type="authorization_error",
            headers=headers
        )


class NotFoundException(CustomHTTPException):
    """Exception for resource not found errors."""
    
    def __init__(
        self,
        detail: str = "Resource not found",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_type="not_found_error",
            headers=headers
        )


class ConflictException(CustomHTTPException):
    """Exception for resource conflict errors."""
    
    def __init__(
        self,
        detail: str = "Resource conflict",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_type="conflict_error",
            headers=headers
        )


class RateLimitException(CustomHTTPException):
    """Exception for rate limit errors."""
    
    def __init__(
        self,
        detail: str = "Rate limit exceeded",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            error_type="rate_limit_error",
            headers=headers
        )


class ExternalAPIException(CustomHTTPException):
    """Exception for external API errors."""
    
    def __init__(
        self,
        detail: str = "External API error",
        service: str = "unknown",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"{service}: {detail}",
            error_type="external_api_error",
            headers=headers
        )
        self.service = service


class DatabaseException(CustomHTTPException):
    """Exception for database errors."""
    
    def __init__(
        self,
        detail: str = "Database operation failed",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_type="database_error",
            headers=headers
        )


class FileUploadException(CustomHTTPException):
    """Exception for file upload errors."""
    
    def __init__(
        self,
        detail: str = "File upload failed",
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_type="file_upload_error",
            headers=headers
        )
