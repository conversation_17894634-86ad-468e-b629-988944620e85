"""
Logging configuration and utilities.
"""
import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional

from app.config.settings import get_settings

settings = get_settings()


class ColoredFormatter(logging.Formatter):
    """Colored log formatter for console output."""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        """Format log record with colors."""
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset_color = self.COLORS['RESET']
        
        # Add color to level name
        record.levelname = f"{log_color}{record.levelname}{reset_color}"
        
        return super().format(record)


def setup_logging():
    """Set up application logging configuration."""
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        settings.log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Console handler with colors (only in debug mode)
    if settings.debug:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name."""
    return logging.getLogger(name)


class RequestLogger:
    """Logger for HTTP requests with structured data."""
    
    def __init__(self, name: str = "request"):
        self.logger = get_logger(name)
    
    def log_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log HTTP request with structured data."""
        extra_data = {
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration": duration,
            "user_id": user_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        message = f"{method} {path} - {status_code} - {duration:.4f}s"
        
        if status_code >= 500:
            self.logger.error(message, extra=extra_data)
        elif status_code >= 400:
            self.logger.warning(message, extra=extra_data)
        else:
            self.logger.info(message, extra=extra_data)


class SecurityLogger:
    """Logger for security-related events."""
    
    def __init__(self, name: str = "security"):
        self.logger = get_logger(name)
    
    def log_login_attempt(
        self,
        email: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log login attempt."""
        status = "SUCCESS" if success else "FAILED"
        message = f"Login attempt for {email}: {status}"
        
        extra_data = {
            "event": "login_attempt",
            "email": email,
            "success": success,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if success:
            self.logger.info(message, extra=extra_data)
        else:
            self.logger.warning(message, extra=extra_data)
    
    def log_registration(
        self,
        email: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log user registration."""
        message = f"New user registration: {email}"
        
        extra_data = {
            "event": "user_registration",
            "email": email,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.info(message, extra=extra_data)
    
    def log_password_reset(
        self,
        email: str,
        ip_address: Optional[str] = None
    ):
        """Log password reset request."""
        message = f"Password reset requested for: {email}"
        
        extra_data = {
            "event": "password_reset_request",
            "email": email,
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.info(message, extra=extra_data)


# Initialize logging on import
setup_logging()
