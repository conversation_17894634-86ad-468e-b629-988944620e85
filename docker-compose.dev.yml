version: "3.8"

services:
  # FastAPI Development Server
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder # Use builder stage for development
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_URL=sqlite:///./app.db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production-must-be-at-least-32-characters-long
      - AMAZON_HOST=webservices.amazon.in
      - AMAZON_REGION=ap-south-1
      # Add your Amazon API credentials here or use .env file
      - AMAZON_ACCESS_KEY=${AMAZON_ACCESS_KEY:-your-access-key}
      - AMAZON_SECRET_KEY=${AMAZON_SECRET_KEY:-your-secret-key}
      - AMAZON_PARTNER_TAG=${AMAZON_PARTNER_TAG:-your-partner-tag}
    volumes:
      # Mount source code for hot reload
      - ./app:/app/app:ro
      - ./tests:/app/tests:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      # Persistent data volumes
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./app.db:/app/app.db
      # UV cache for faster rebuilds
      - uv_cache:/tmp/uv-cache
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Installing development dependencies...' &&
        uv pip install -e '.[dev]' &&
        echo 'Starting FastAPI development server...' &&
        uvicorn app.main:app
        --reload
        --host 0.0.0.0
        --port 8000
        --log-level debug
        --access-log
        --use-colors
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - app-network

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network

  # PostgreSQL for production-like testing (optional)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ukcheapdeal_dev
      - POSTGRES_USER=dev_user
      - POSTGRES_PASSWORD=dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev_user -d ukcheapdeal_dev"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
    profiles:
      - postgres # Only start with --profile postgres

  # Test runner container
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    volumes:
      - ./:/app
      - uv_cache:/tmp/uv-cache
    working_dir: /app
    environment:
      - ENVIRONMENT=testing
      - DATABASE_URL=sqlite:///./test.db
    command: >
      sh -c "
        echo 'Installing test dependencies...' &&
        uv pip install -e '.[dev]' &&
        echo 'Running tests...' &&
        pytest tests/ -v --cov=app --cov-report=html --cov-report=term-missing
      "
    depends_on:
      - redis
    networks:
      - app-network
    profiles:
      - test # Only start with --profile test

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  uv_cache:
    driver: local

networks:
  app-network:
    driver: bridge
