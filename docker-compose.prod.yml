version: "3.8"

services:
  # FastAPI Production Server
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=${DATABASE_URL:-postgresql://prod_user:${DB_PASSWORD}@postgres:5432/ukcheapdeal_prod}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - AMAZON_HOST=webservices.amazon.in
      - AMAZON_REGION=ap-south-1
      - AMAZON_ACCESS_KEY=${AMAZON_ACCESS_KEY}
      - AMAZON_SECRET_KEY=${AMAZON_SECRET_KEY}
      - AMAZON_PARTNER_TAG=${AMAZON_PARTNER_TAG}
      - SENTRY_DSN=${SENTRY_DSN:-}
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - app_data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Production PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=ukcheapdeal_prod
      - POSTGRES_USER=prod_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-prod-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./backups:/backups
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2G
        reservations:
          cpus: "0.5"
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U prod_user -d ukcheapdeal_prod"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Security: Don't expose port in production
    # ports:
    #   - "5432:5432"

  # Production Redis
  redis:
    image: redis:7-alpine
    command: >
      redis-server 
      --appendonly yes 
      --appendfsync everysec 
      --maxmemory 512mb 
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
    healthcheck:
      test:
        [
          "CMD",
          "redis-cli",
          "--no-auth-warning",
          "-a",
          "${REDIS_PASSWORD}",
          "ping",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.25"
          memory: 128M
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    profiles:
      - nginx # Only start with --profile nginx

  # Database backup service (optional)
  db-backup:
    image: postgres:15-alpine
    environment:
      - PGPASSWORD=${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: >
      sh -c "
        chmod +x /backup.sh &&
        while true; do
          /backup.sh
          sleep 86400  # Run daily
        done
      "
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - backup # Only start with --profile backup

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_data:
    driver: local

networks:
  app-network:
    driver: bridge

# Production secrets (use Docker secrets in production)
secrets:
  db_password:
    external: true
  redis_password:
    external: true
  secret_key:
    external: true
