version: '3.8'

services:
  # Simplified FastAPI Development Server for testing
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_URL=sqlite:///./app.db
      - SECRET_KEY=dev-secret-key-change-in-production-must-be-at-least-32-characters-long
      - AMAZON_HOST=webservices.amazon.in
      - AMAZON_REGION=ap-south-1
      - AMAZON_ACCESS_KEY=dummy-access-key
      - AMAZON_SECRET_KEY=dummy-secret-key
      - AMAZON_PARTNER_TAG=dummy-partner-tag
    volumes:
      # Mount source code for hot reload
      - ./app:/app/app:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      # Persistent data volumes
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting FastAPI application...' &&
        uv --version &&
        uv pip install -e '.[dev]' &&
        echo 'Dependencies installed, starting server...' &&
        uvicorn app.main:app 
        --reload 
        --host 0.0.0.0 
        --port 8000 
        --log-level debug
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Simple Redis for basic testing
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local
