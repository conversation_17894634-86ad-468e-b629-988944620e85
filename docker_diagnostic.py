#!/usr/bin/env python3
"""
Docker diagnostic script for FastAPI e-commerce backend.
This script helps identify and fix Docker-related issues.
"""
import os
import sys
import subprocess
import json
from pathlib import Path


def print_banner():
    """Print diagnostic banner."""
    print("🐳 Docker Environment Diagnostic Tool")
    print("=" * 50)


def check_docker_availability():
    """Check if Docker is available and running."""
    print("🔍 Checking Docker availability...")
    
    try:
        # Check Docker version
        result = subprocess.run(["docker", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Docker: {result.stdout.strip()}")
        else:
            print("❌ Docker not available")
            return False
        
        # Check Docker Compose version
        result = subprocess.run(["docker-compose", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Docker Compose: {result.stdout.strip()}")
        else:
            print("❌ Docker Compose not available")
            return False
        
        # Check if Docker daemon is running
        result = subprocess.run(["docker", "info"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Docker daemon is running")
            return True
        else:
            print("❌ Docker daemon not running")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Docker commands timed out")
        return False
    except FileNotFoundError:
        print("❌ Docker not installed")
        return False
    except Exception as e:
        print(f"❌ Docker check failed: {e}")
        return False


def check_required_files():
    """Check if all required files exist."""
    print("\n📁 Checking required files...")
    
    required_files = [
        "docker-compose.dev.yml",
        "Dockerfile",
        "pyproject.toml",
        "app/main.py",
        "app/config/settings.py",
        "redis.conf"
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0


def check_docker_compose_syntax():
    """Check Docker Compose file syntax."""
    print("\n🔧 Checking Docker Compose syntax...")
    
    try:
        result = subprocess.run([
            "docker-compose", "-f", "docker-compose.dev.yml", "config"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Docker Compose syntax is valid")
            return True
        else:
            print("❌ Docker Compose syntax errors:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Docker Compose config check timed out")
        return False
    except Exception as e:
        print(f"❌ Docker Compose check failed: {e}")
        return False


def check_port_availability():
    """Check if required ports are available."""
    print("\n🔌 Checking port availability...")
    
    import socket
    
    ports_to_check = [8000, 6379]
    
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"⚠️ Port {port} is already in use")
        else:
            print(f"✅ Port {port} is available")


def create_missing_directories():
    """Create missing directories."""
    print("\n📁 Creating missing directories...")
    
    directories = ["logs", "uploads", "scripts"]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ Created directory: {directory}")
            except Exception as e:
                print(f"❌ Failed to create {directory}: {e}")
        else:
            print(f"✅ Directory exists: {directory}")


def check_environment_variables():
    """Check environment variables."""
    print("\n🌍 Checking environment variables...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file exists")
        
        # Read and validate key variables
        try:
            with open(env_file, 'r') as f:
                content = f.read()
                
            if "SECRET_KEY" in content:
                print("✅ SECRET_KEY found in .env")
            else:
                print("⚠️ SECRET_KEY not found in .env")
                
            if "DATABASE_URL" in content:
                print("✅ DATABASE_URL found in .env")
            else:
                print("⚠️ DATABASE_URL not found in .env")
                
        except Exception as e:
            print(f"❌ Error reading .env file: {e}")
    else:
        print("⚠️ .env file not found - using defaults from docker-compose")


def test_docker_build():
    """Test Docker build process."""
    print("\n🏗️ Testing Docker build...")
    
    try:
        # Test building just the builder stage
        result = subprocess.run([
            "docker", "build", "--target", "builder", "-t", "test-build", "."
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Docker build successful")
            
            # Clean up test image
            subprocess.run(["docker", "rmi", "test-build"], 
                         capture_output=True, text=True)
            return True
        else:
            print("❌ Docker build failed:")
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
            print("STDERR:", result.stderr[-1000:])  # Last 1000 chars
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Docker build timed out (>5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Docker build test failed: {e}")
        return False


def check_docker_compose_services():
    """Check Docker Compose services status."""
    print("\n🔍 Checking Docker Compose services...")
    
    try:
        result = subprocess.run([
            "docker-compose", "-f", "docker-compose.dev.yml", "ps"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Docker Compose services status:")
            print(result.stdout)
            return True
        else:
            print("❌ Failed to get services status:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Services check failed: {e}")
        return False


def generate_fix_recommendations():
    """Generate fix recommendations based on findings."""
    print("\n💡 Fix Recommendations:")
    print("=" * 30)
    
    print("1. If Docker is not running:")
    print("   - Start Docker Desktop or Docker daemon")
    print("   - Verify Docker installation")
    
    print("\n2. If ports are in use:")
    print("   - Stop conflicting services: lsof -i :8000")
    print("   - Kill processes: kill -9 <PID>")
    
    print("\n3. If build fails:")
    print("   - Check internet connection for UV installation")
    print("   - Verify Dockerfile syntax")
    print("   - Check available disk space")
    
    print("\n4. If environment issues:")
    print("   - Create .env file with required variables")
    print("   - Ensure SECRET_KEY is at least 32 characters")
    
    print("\n5. If services fail to start:")
    print("   - Check logs: docker-compose -f docker-compose.dev.yml logs")
    print("   - Verify all dependencies are properly installed")


def main():
    """Run all diagnostic checks."""
    print_banner()
    
    checks = [
        ("Docker Availability", check_docker_availability),
        ("Required Files", check_required_files),
        ("Docker Compose Syntax", check_docker_compose_syntax),
        ("Port Availability", check_port_availability),
    ]
    
    results = []
    
    # Run basic checks
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} failed with exception: {e}")
            results.append((check_name, False))
    
    # Create missing directories
    create_missing_directories()
    
    # Check environment
    check_environment_variables()
    
    # Advanced checks if basic ones pass
    basic_checks_passed = all(result for _, result in results)
    
    if basic_checks_passed:
        print("\n🚀 Basic checks passed. Running advanced tests...")
        
        # Test Docker build
        build_success = test_docker_build()
        results.append(("Docker Build", build_success))
        
        # Check services if they're running
        check_docker_compose_services()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Diagnostic Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! Docker environment should work.")
        print("\n🚀 Try starting the environment:")
        print("   docker-compose -f docker-compose.dev.yml up -d --build")
    else:
        print("⚠️ Some checks failed. See recommendations below.")
        generate_fix_recommendations()
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
