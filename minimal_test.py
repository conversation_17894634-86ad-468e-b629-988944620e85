#!/usr/bin/env python3
"""
Minimal test to verify FastAPI application can start.
This bypasses <PERSON><PERSON> and tests the core application directly.
"""
import os
import sys
import tempfile
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def setup_minimal_environment():
    """Set up minimal environment for testing."""
    print("🔧 Setting up minimal test environment...")
    
    # Set required environment variables
    os.environ.update({
        'ENVIRONMENT': 'testing',
        'DEBUG': 'true',
        'SECRET_KEY': 'test-secret-key-for-testing-only-must-be-32-chars-long',
        'DATABASE_URL': 'sqlite:///./test_minimal.db',
        'AMAZON_ACCESS_KEY': 'dummy-access-key',
        'AMAZON_SECRET_KEY': 'dummy-secret-key',
        'AMAZON_PARTNER_TAG': 'dummy-partner-tag',
        'AMAZON_HOST': 'webservices.amazon.in',
        'AMAZON_REGION': 'ap-south-1',
    })
    
    # Create necessary directories
    for directory in ['logs', 'uploads']:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Environment setup complete")


def test_core_imports():
    """Test that core modules can be imported."""
    print("\n🔍 Testing core imports...")
    
    try:
        # Test basic imports
        import fastapi
        import uvicorn
        import pydantic
        import sqlalchemy
        print("✅ Core dependencies available")
        
        # Test application imports
        from app.config.settings import get_settings
        print("✅ Settings import successful")
        
        from app.main import app
        print("✅ FastAPI app import successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_settings_configuration():
    """Test settings configuration."""
    print("\n⚙️ Testing settings configuration...")
    
    try:
        from app.config.settings import get_settings
        
        settings = get_settings()
        print(f"✅ App name: {settings.app_name}")
        print(f"✅ Environment: {settings.environment}")
        print(f"✅ Debug mode: {settings.debug}")
        print(f"✅ Database URL: {settings.database_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings error: {e}")
        return False


def test_fastapi_app_creation():
    """Test FastAPI app creation."""
    print("\n🚀 Testing FastAPI app creation...")
    
    try:
        from app.main import app
        
        print(f"✅ App type: {type(app)}")
        print(f"✅ App title: {app.title}")
        
        # Check routes
        routes = [route.path for route in app.routes]
        print(f"✅ Total routes: {len(routes)}")
        
        # Check for essential routes
        essential_routes = ["/health", "/docs", "/openapi.json"]
        for route in essential_routes:
            if any(route in r for r in routes):
                print(f"✅ Route found: {route}")
            else:
                print(f"⚠️ Route missing: {route}")
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI app error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_models():
    """Test database models."""
    print("\n🗄️ Testing database models...")
    
    try:
        from app.models.user import User, UserRole
        from app.models.store import Store
        from app.database.connection import Base, engine
        
        print("✅ Models imported successfully")
        print(f"✅ UserRole enum: {list(UserRole)}")
        
        # Test table creation
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created")
        
        return True
        
    except Exception as e:
        print(f"❌ Database models error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_health_endpoint():
    """Test health endpoint."""
    print("\n🏥 Testing health endpoint...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        response = client.get("/health")
        
        print(f"✅ Health endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health response: {data}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_documentation():
    """Test API documentation endpoints."""
    print("\n📚 Testing API documentation...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        
        # Test OpenAPI schema
        response = client.get("/openapi.json")
        print(f"✅ OpenAPI schema status: {response.status_code}")
        
        # Test docs endpoint
        response = client.get("/docs")
        print(f"✅ Docs endpoint status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API documentation error: {e}")
        return False


def cleanup():
    """Clean up test files."""
    print("\n🧹 Cleaning up...")
    
    try:
        # Remove test database
        test_db = Path("test_minimal.db")
        if test_db.exists():
            test_db.unlink()
            print("✅ Test database removed")
        
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


def main():
    """Run minimal tests."""
    print("🧪 FastAPI Minimal Test Suite")
    print("=" * 40)
    
    # Setup environment
    setup_minimal_environment()
    
    # Define tests
    tests = [
        ("Core Imports", test_core_imports),
        ("Settings Configuration", test_settings_configuration),
        ("FastAPI App Creation", test_fastapi_app_creation),
        ("Database Models", test_database_models),
        ("Health Endpoint", test_health_endpoint),
        ("API Documentation", test_api_documentation),
    ]
    
    results = []
    
    # Run tests
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    cleanup()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results Summary:")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Application core is working.")
        print("\n🐳 Ready for Docker deployment:")
        print("   docker-compose -f docker-compose.simple.yml up --build")
    else:
        print("⚠️ Some tests failed. Fix issues before Docker deployment.")
        print("\n💡 Common fixes:")
        print("   - Install missing dependencies")
        print("   - Check Python version (3.8+ required)")
        print("   - Verify all required files exist")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
