#!/bin/bash

# Quick fix script for Docker issues
echo "🔧 Quick Fix for Docker Issues"
echo "=============================="

# Fix 1: Create required directories
echo "📁 Creating required directories..."
mkdir -p logs uploads scripts
echo "✅ Directories created"

# Fix 2: Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose -f docker-compose.dev.yml down -v 2>/dev/null || true
echo "✅ Cleanup complete"

# Fix 3: Try building with alternative Dockerfile first
echo "🏗️ Testing alternative Dockerfile..."
if docker build -f Dockerfile.alternative --target builder -t test-uv-build . ; then
    echo "✅ Alternative Dockerfile works! Using it for development."
    
    # Create a temporary docker-compose file using the alternative Dockerfile
    cat > docker-compose.fix.yml << 'EOF'
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.alternative
      target: builder
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./app:/app/app:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Installing dependencies...' &&
        uv pip install -e '.[dev]' &&
        echo 'Starting FastAPI server...' &&
        uvicorn app.main:app 
        --reload 
        --host 0.0.0.0 
        --port 8000 
        --log-level debug
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
EOF

    echo "🚀 Starting services with fixed configuration..."
    docker-compose -f docker-compose.fix.yml up -d --build
    
    echo "⏳ Waiting for services to start..."
    sleep 10
    
    echo "📊 Checking service status..."
    docker-compose -f docker-compose.fix.yml ps
    
    echo "📋 Checking logs..."
    docker-compose -f docker-compose.fix.yml logs app
    
    echo ""
    echo "🎉 If no errors above, try accessing:"
    echo "   Health: http://localhost:8000/health"
    echo "   Docs:   http://localhost:8000/docs"
    echo ""
    echo "📝 To view logs: docker-compose -f docker-compose.fix.yml logs -f app"
    echo "🛑 To stop: docker-compose -f docker-compose.fix.yml down"
    
else
    echo "❌ Alternative Dockerfile also failed. Let's try a simpler approach..."
    
    # Create a minimal Dockerfile without UV
    cat > Dockerfile.minimal << 'EOF'
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy project files
COPY pyproject.toml ./
COPY app/ ./app/

# Install dependencies with pip
RUN pip install --upgrade pip && \
    pip install -e ".[dev]"

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

    # Create minimal docker-compose
    cat > docker-compose.minimal.yml << 'EOF'
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.minimal
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - ./app:/app/app:ro
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
EOF

    echo "🚀 Starting with minimal configuration (using pip instead of UV)..."
    docker-compose -f docker-compose.minimal.yml up -d --build
    
    echo "⏳ Waiting for services to start..."
    sleep 15
    
    echo "📊 Checking service status..."
    docker-compose -f docker-compose.minimal.yml ps
    
    echo "📋 Checking logs..."
    docker-compose -f docker-compose.minimal.yml logs app
    
    echo ""
    echo "🎉 If no errors above, try accessing:"
    echo "   Health: http://localhost:8000/health"
    echo "   Docs:   http://localhost:8000/docs"
    echo ""
    echo "📝 To view logs: docker-compose -f docker-compose.minimal.yml logs -f app"
    echo "🛑 To stop: docker-compose -f docker-compose.minimal.yml down"
fi

echo ""
echo "🔍 Troubleshooting tips:"
echo "1. Check if ports 8000 and 6379 are free: lsof -i :8000"
echo "2. View detailed logs: docker logs <container_name>"
echo "3. Test health endpoint: curl http://localhost:8000/health"
echo "4. If still failing, check the TROUBLESHOOTING.md file"
