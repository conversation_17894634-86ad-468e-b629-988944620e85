#!/bin/bash

# UK Cheap Deal Backend - Legacy Startup Script
# This script is deprecated. Please use the new UV-based startup methods.

set -e

echo "⚠️  DEPRECATION NOTICE"
echo "This script is deprecated and will be removed in a future version."
echo "Please use the new UV-based startup methods:"
echo ""
echo "Recommended options:"
echo "1. Docker (Recommended):"
echo "   ./scripts/docker-dev.sh start"
echo ""
echo "2. UV Package Manager:"
echo "   ./run_uv.sh"
echo "   # OR"
echo "   python3 start_uv.py"
echo ""
echo "3. Manual with UV:"
echo "   uv pip install -e '.[dev]'"
echo "   uv run uvicorn app.main:app --reload"
echo ""
echo "Continuing with legacy pip-based installation..."
echo ""

# Check if Python 3 is available
if ! command -v python3 &>/dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies from pyproject.toml
echo "📦 Installing dependencies..."
pip install -e ".[dev]"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs uploads

# Run the application
echo "🚀 Starting FastAPI application..."
echo "📖 API Documentation: http://localhost:8000/docs"
echo "🏥 Health Check: http://localhost:8000/health"
echo "Press Ctrl+C to stop the server"
echo "--------------------------------------------------"

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
