#!/bin/bash

# UV-based startup script for UK Cheap Deal Backend
# This script sets up the environment using UV package manager

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_banner() {
    echo -e "${BLUE}"
    echo "🚀 UK Cheap Deal Backend - FastAPI E-commerce Application"
    echo "📦 Using UV Package Manager for Lightning-Fast Dependencies"
    echo "================================================================"
    echo -e "${NC}"
}

# Check if running on Linux
check_os() {
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        print_error "This application is optimized for Linux deployment only"
        print_info "Please use a Linux environment or WSL2 on Windows"
        exit 1
    fi
    print_status "Linux environment detected"
}

# Check Python version
check_python() {
    print_info "Checking Python version..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    REQUIRED_VERSION="3.8"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_error "Python 3.8 or higher is required. Current version: $PYTHON_VERSION"
        exit 1
    fi
    
    print_status "Python $PYTHON_VERSION detected"
}

# Install UV if not present
install_uv() {
    print_info "Checking UV installation..."
    
    if command -v uv &> /dev/null; then
        UV_VERSION=$(uv --version)
        print_status "UV is already installed: $UV_VERSION"
        return 0
    fi
    
    print_info "Installing UV package manager..."
    
    # Install UV using the official installer
    if curl -LsSf https://astral.sh/uv/install.sh | sh; then
        print_status "UV installed successfully"
        
        # Add UV to PATH for current session
        export PATH="$HOME/.cargo/bin:$PATH"
        
        # Verify installation
        if command -v uv &> /dev/null; then
            UV_VERSION=$(uv --version)
            print_status "UV verification successful: $UV_VERSION"
        else
            print_error "UV installation verification failed"
            print_info "Please restart your terminal or run: source ~/.bashrc"
            exit 1
        fi
    else
        print_error "Failed to install UV"
        print_info "Please install UV manually: curl -LsSf https://astral.sh/uv/install.sh | sh"
        exit 1
    fi
}

# Set up virtual environment and install dependencies
setup_environment() {
    print_info "Setting up development environment..."
    
    # Create virtual environment if it doesn't exist
    if [ ! -d ".venv" ]; then
        print_info "Creating virtual environment..."
        uv venv .venv
        print_status "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi
    
    # Install dependencies
    print_info "Installing dependencies with UV..."
    
    # Try to install with development dependencies first
    if uv pip install -e ".[dev]"; then
        print_status "All dependencies installed successfully"
    else
        print_warning "Failed to install development dependencies, trying core only..."
        if uv pip install -e "."; then
            print_status "Core dependencies installed successfully"
        else
            print_error "Failed to install dependencies"
            exit 1
        fi
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating application directories..."
    
    directories=("logs" "uploads" ".uv-cache" "htmlcov")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
}

# Set up environment file
setup_env_file() {
    print_info "Checking environment configuration..."
    
    if [ ! -f ".env" ] && [ -f ".env.example" ]; then
        print_info "Creating .env file from template..."
        cp .env.example .env
        print_status "Environment file created"
        print_warning "Please update .env with your actual configuration"
    elif [ -f ".env" ]; then
        print_status "Environment file found"
    else
        print_warning "No environment file found. Using defaults."
    fi
}

# Run tests
run_tests() {
    print_info "Running application tests..."
    
    if uv run pytest tests/ -v --tb=short --maxfail=5; then
        print_status "All tests passed"
    else
        print_warning "Some tests failed, but continuing with startup"
        print_info "Please review test failures when convenient"
    fi
}

# Start the FastAPI server
start_server() {
    print_info "Starting FastAPI server..."
    echo
    print_info "📖 API Documentation: http://localhost:8000/docs"
    print_info "🏥 Health Check: http://localhost:8000/health"
    print_info "🔧 Admin Panel: http://localhost:8000/admin"
    print_info "📊 Metrics: http://localhost:8000/metrics"
    echo
    print_info "Press Ctrl+C to stop the server"
    echo "================================================================"
    
    # Start server with UV
    uv run uvicorn app.main:app \
        --reload \
        --host 0.0.0.0 \
        --port 8000 \
        --log-level info \
        --access-log \
        --use-colors
}

# Cleanup function
cleanup() {
    print_info "Shutting down server..."
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_banner
    
    check_os
    check_python
    install_uv
    setup_environment
    create_directories
    setup_env_file
    
    # Ask user if they want to run tests
    echo
    read -p "🧪 Run tests before starting server? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    echo
    print_status "All setup completed! Starting server..."
    echo "================================================================"
    
    start_server
}

# Run main function
main "$@"
