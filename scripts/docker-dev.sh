#!/bin/bash

# Development Docker management script
# This script provides easy commands for managing the development environment

set -e
set +u # Disable unset variable errors to fix bash completion issues

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_banner() {
    echo -e "${BLUE}"
    echo "🚀 UK Cheap Deal Backend - Development Environment"
    echo "📦 Docker Compose Management Script"
    echo "=================================================="
    echo -e "${NC}"
}

# Check if Docker and Docker Compose are installed
check_dependencies() {
    print_info "Checking dependencies..."

    if ! command -v docker &>/dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi

    if ! command -v docker-compose &>/dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi

    print_status "Dependencies check passed"
}

# Start development environment
start_dev() {
    print_info "Starting development environment..."

    # Create necessary directories
    mkdir -p logs uploads

    # Start services
    docker-compose -f docker-compose.dev.yml up -d

    print_status "Development environment started"
    print_info "API Documentation: http://localhost:8000/docs"
    print_info "Health Check: http://localhost:8000/health"
    print_info "Redis: localhost:6379"
}

# Stop development environment
stop_dev() {
    print_info "Stopping development environment..."
    docker-compose -f docker-compose.dev.yml down
    print_status "Development environment stopped"
}

# Restart development environment
restart_dev() {
    print_info "Restarting development environment..."
    stop_dev
    start_dev
}

# View logs
logs_dev() {
    service=${1:-app}
    print_info "Showing logs for service: $service"
    docker-compose -f docker-compose.dev.yml logs -f "$service"
}

# Run tests
test_dev() {
    print_info "Running tests in development environment..."
    docker-compose -f docker-compose.dev.yml --profile test up test-runner
}

# Start with specific profiles
start_with_profile() {
    profile=$1
    print_info "Starting development environment with profile: $profile"

    case $profile in
    "postgres")
        docker-compose -f docker-compose.dev.yml --profile postgres up -d
        print_info "PostgreSQL: localhost:5432"
        ;;
    "tools")
        docker-compose -f docker-compose.dev.yml --profile tools up -d
        print_info "Development tools container started"
        ;;
    "docs")
        docker-compose -f docker-compose.dev.yml --profile docs up -d
        print_info "Documentation server: http://localhost:8080"
        ;;
    "test")
        docker-compose -f docker-compose.dev.yml --profile test up test-runner
        ;;
    *)
        print_error "Unknown profile: $profile"
        print_info "Available profiles: postgres, tools, docs, test"
        exit 1
        ;;
    esac
}

# Execute command in app container
exec_app() {
    print_info "Executing command in app container: $*"
    docker-compose -f docker-compose.dev.yml exec app "$@"
}

# Execute command in dev-tools container
exec_tools() {
    print_info "Executing command in dev-tools container: $*"
    docker-compose -f docker-compose.dev.yml --profile tools exec dev-tools "$@"
}

# Install UV dependencies
install_deps() {
    print_info "Installing dependencies with UV..."
    exec_app uv pip install -e ".[dev]"
    print_status "Dependencies installed"
}

# Run code formatting
format_code() {
    print_info "Formatting code..."
    exec_app black app/ tests/
    exec_app isort app/ tests/
    print_status "Code formatted"
}

# Run linting
lint_code() {
    print_info "Running linting..."
    exec_app flake8 app/ tests/
    exec_app mypy app/
    print_status "Linting completed"
}

# Clean up development environment
clean_dev() {
    print_warning "This will remove all containers, volumes, and images"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning up development environment..."
        docker-compose -f docker-compose.dev.yml down -v --rmi all
        docker system prune -f
        print_status "Development environment cleaned"
    else
        print_info "Cleanup cancelled"
    fi
}

# Show status
status_dev() {
    print_info "Development environment status:"
    docker-compose -f docker-compose.dev.yml ps
}

# Show help
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start                 Start development environment"
    echo "  stop                  Stop development environment"
    echo "  restart               Restart development environment"
    echo "  logs [service]        Show logs (default: app)"
    echo "  test                  Run tests"
    echo "  profile [name]        Start with specific profile (postgres|tools|docs|test)"
    echo "  exec [command]        Execute command in app container"
    echo "  tools [command]       Execute command in dev-tools container"
    echo "  install               Install dependencies with UV"
    echo "  format                Format code with black and isort"
    echo "  lint                  Run linting with flake8 and mypy"
    echo "  status                Show environment status"
    echo "  clean                 Clean up environment (removes everything)"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start              # Start development environment"
    echo "  $0 logs app           # Show app logs"
    echo "  $0 profile postgres   # Start with PostgreSQL"
    echo "  $0 exec bash          # Open bash in app container"
    echo "  $0 tools pytest      # Run pytest in tools container"
}

# Main script logic
main() {
    print_banner
    check_dependencies

    case "${1:-help}" in
    "start")
        start_dev
        ;;
    "stop")
        stop_dev
        ;;
    "restart")
        restart_dev
        ;;
    "logs")
        logs_dev "$2"
        ;;
    "test")
        test_dev
        ;;
    "profile")
        start_with_profile "$2"
        ;;
    "exec")
        shift
        exec_app "$@"
        ;;
    "tools")
        shift
        exec_tools "$@"
        ;;
    "install")
        install_deps
        ;;
    "format")
        format_code
        ;;
    "lint")
        lint_code
        ;;
    "status")
        status_dev
        ;;
    "clean")
        clean_dev
        ;;
    "help" | *)
        show_help
        ;;
    esac
}

# Run main function
main "$@"
