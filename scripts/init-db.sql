-- Development database initialization script
-- This script sets up the development database with initial data

-- Create extensions if needed
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create development user if not exists (for PostgreSQL)
-- Note: This is handled by environment variables in Docker

-- Insert initial stores
INSERT INTO stores (name, display_name, base_url, description, is_active, created_at, updated_at) 
VALUES 
    ('amazon_in', 'Amazon India', 'https://www.amazon.in', 'Amazon India marketplace', true, NOW(), NOW()),
    ('flipkart', 'Flipkart', 'https://www.flipkart.com', 'Flipkart marketplace', true, NOW(), NOW()),
    ('myntra', 'Myntra', 'https://www.myntra.com', 'Fashion and lifestyle store', true, NOW(), NOW()),
    ('ajio', 'AJIO', 'https://www.ajio.com', 'Fashion and lifestyle store', true, NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- Insert initial categories (if categories table exists)
-- INSERT INTO categories (name, display_name, description, is_active, created_at, updated_at)
-- VALUES 
--     ('electronics', 'Electronics', 'Electronic devices and gadgets', true, NOW(), NOW()),
--     ('fashion', 'Fashion', 'Clothing and accessories', true, NOW(), NOW()),
--     ('home', 'Home & Kitchen', 'Home and kitchen items', true, NOW(), NOW()),
--     ('books', 'Books', 'Books and literature', true, NOW(), NOW()),
--     ('sports', 'Sports & Fitness', 'Sports and fitness equipment', true, NOW(), NOW())
-- ON CONFLICT (name) DO NOTHING;

-- Create development admin user
-- Password: admin123 (hashed with bcrypt)
INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, is_verified, created_at, updated_at)
VALUES (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e',
    'Dev',
    'Admin',
    'superadmin',
    true,
    true,
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Create development test user
-- Password: user123 (hashed with bcrypt)
INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, is_verified, created_at, updated_at)
VALUES (
    '<EMAIL>',
    '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'Dev',
    'User',
    'user',
    true,
    true,
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert sample products for development
-- Note: This assumes products table structure
-- INSERT INTO products (title, description, store_id, brand, price, original_price, is_available, is_featured, is_active, created_at, updated_at)
-- SELECT 
--     'Sample Product ' || generate_series,
--     'This is a sample product for development testing',
--     (SELECT id FROM stores WHERE name = 'amazon_in' LIMIT 1),
--     'Sample Brand',
--     999.99,
--     1299.99,
--     true,
--     (generate_series % 3 = 0),  -- Every 3rd product is featured
--     true,
--     NOW(),
--     NOW()
-- FROM generate_series(1, 10)
-- ON CONFLICT DO NOTHING;

-- Create indexes for better performance
-- CREATE INDEX IF NOT EXISTS idx_products_store_id ON products(store_id);
-- CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
-- CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
-- CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
-- CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
-- CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Development-specific settings
-- Set timezone
-- SET timezone = 'UTC';

-- Enable query logging for development (PostgreSQL)
-- ALTER SYSTEM SET log_statement = 'all';
-- ALTER SYSTEM SET log_min_duration_statement = 0;
-- SELECT pg_reload_conf();

COMMIT;
