#!/bin/bash

# Test Docker Setup Script
# This script tests both development and production Docker configurations

set -e

echo "🐳 Testing Docker Setup for UK Cheap Deal Backend"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Docker is running
echo "Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

print_status "Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_warning "docker-compose not found, trying docker compose"
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

print_status "Docker Compose is available"

# Test 1: Build the development image
echo ""
echo "🔨 Building development image..."
if docker build --target builder -t ukcheapdeal-backend:dev . > /tmp/docker-build-dev.log 2>&1; then
    print_status "Development image built successfully"
else
    print_error "Failed to build development image"
    echo "Build log:"
    cat /tmp/docker-build-dev.log
    exit 1
fi

# Test 2: Build the production image
echo ""
echo "🔨 Building production image..."
if docker build --target production -t ukcheapdeal-backend:prod . > /tmp/docker-build-prod.log 2>&1; then
    print_status "Production image built successfully"
else
    print_error "Failed to build production image"
    echo "Build log:"
    cat /tmp/docker-build-prod.log
    exit 1
fi

# Test 3: Validate docker-compose files
echo ""
echo "📋 Validating docker-compose configurations..."

if $COMPOSE_CMD -f docker-compose.dev.yml config > /dev/null 2>&1; then
    print_status "Development docker-compose.yml is valid"
else
    print_error "Development docker-compose.yml has syntax errors"
    $COMPOSE_CMD -f docker-compose.dev.yml config
    exit 1
fi

if $COMPOSE_CMD -f docker-compose.prod.yml config > /dev/null 2>&1; then
    print_status "Production docker-compose.yml is valid"
else
    print_error "Production docker-compose.yml has syntax errors"
    $COMPOSE_CMD -f docker-compose.prod.yml config
    exit 1
fi

# Test 4: Test development environment startup (quick test)
echo ""
echo "🚀 Testing development environment startup..."
export AMAZON_ACCESS_KEY="test-key"
export AMAZON_SECRET_KEY="test-secret"
export AMAZON_PARTNER_TAG="test-tag"

if timeout 60 $COMPOSE_CMD -f docker-compose.dev.yml up -d > /tmp/docker-dev-up.log 2>&1; then
    print_status "Development environment started successfully"
    
    # Wait a bit for services to be ready
    sleep 10
    
    # Check if services are running
    if $COMPOSE_CMD -f docker-compose.dev.yml ps | grep -q "Up"; then
        print_status "Development services are running"
    else
        print_warning "Some development services may not be running properly"
    fi
    
    # Cleanup
    $COMPOSE_CMD -f docker-compose.dev.yml down > /dev/null 2>&1
    print_status "Development environment cleaned up"
else
    print_error "Failed to start development environment"
    echo "Startup log:"
    cat /tmp/docker-dev-up.log
    # Cleanup on failure
    $COMPOSE_CMD -f docker-compose.dev.yml down > /dev/null 2>&1
fi

# Test 5: Check image sizes
echo ""
echo "📊 Docker image information:"
echo "Development image:"
docker images ukcheapdeal-backend:dev --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
echo "Production image:"
docker images ukcheapdeal-backend:prod --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Test 6: Security scan (if available)
echo ""
echo "🔒 Security information:"
if command -v docker scan &> /dev/null; then
    echo "Running security scan on production image..."
    docker scan ukcheapdeal-backend:prod || print_warning "Security scan completed with warnings"
else
    print_warning "Docker scan not available - consider running security scans in CI/CD"
fi

# Cleanup temporary files
rm -f /tmp/docker-build-dev.log /tmp/docker-build-prod.log /tmp/docker-dev-up.log

echo ""
echo "🎉 Docker setup test completed successfully!"
echo ""
echo "Next steps:"
echo "1. For development: $COMPOSE_CMD -f docker-compose.dev.yml up -d"
echo "2. For production: $COMPOSE_CMD -f docker-compose.prod.yml up -d"
echo "3. For testing: $COMPOSE_CMD -f docker-compose.dev.yml --profile test up test-runner"
echo "4. For PostgreSQL: $COMPOSE_CMD -f docker-compose.dev.yml --profile postgres up -d"
echo ""
echo "Environment variables needed for production:"
echo "- DATABASE_URL"
echo "- SECRET_KEY"
echo "- DB_PASSWORD"
echo "- REDIS_PASSWORD"
echo "- AMAZON_ACCESS_KEY"
echo "- AMAZON_SECRET_KEY"
echo "- AMAZON_PARTNER_TAG"
