#!/usr/bin/env python3
"""
UV-based startup script for the FastAPI e-commerce backend.
This script handles UV installation, dependency management, and application startup.
"""
import sys
import os
import subprocess
import shutil
import platform
from pathlib import Path


def print_banner():
    """Print application banner."""
    print("🚀 UK Cheap Deal Backend - FastAPI E-commerce Application")
    print("📦 Using UV Package Manager for Lightning-Fast Dependencies")
    print("=" * 70)


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version.split()[0]}")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True


def check_uv_installation():
    """Check if UV is installed and install if necessary."""
    print("🔍 Checking UV installation...")
    
    if shutil.which("uv"):
        try:
            result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ UV is installed: {result.stdout.strip()}")
                return True
        except Exception:
            pass
    
    print("📦 UV not found. Installing UV...")
    return install_uv()


def install_uv():
    """Install UV package manager."""
    try:
        if platform.system() == "Windows":
            print("❌ This application is optimized for Linux deployment only")
            print("   Please use WSL2 or a Linux environment")
            return False
        
        # Install UV using the official installer
        print("⬇️ Downloading and installing UV...")
        install_cmd = [
            "curl", "-LsSf", "https://astral.sh/uv/install.sh", "|", "sh"
        ]
        
        # Use shell=True for pipe command
        result = subprocess.run(
            "curl -LsSf https://astral.sh/uv/install.sh | sh",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print(f"❌ Failed to install UV: {result.stderr}")
            print("💡 Please install UV manually:")
            print("   curl -LsSf https://astral.sh/uv/install.sh | sh")
            return False
        
        # Add UV to PATH for current session
        uv_bin = Path.home() / ".cargo" / "bin"
        if uv_bin.exists():
            os.environ["PATH"] = f"{uv_bin}:{os.environ.get('PATH', '')}"
        
        print("✅ UV installed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error installing UV: {e}")
        return False


def setup_environment():
    """Set up the development environment with UV."""
    print("🔧 Setting up development environment...")
    
    try:
        # Create virtual environment with UV
        print("📁 Creating virtual environment...")
        result = subprocess.run(["uv", "venv", ".venv"], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"⚠️ Virtual environment creation: {result.stderr}")
        
        # Install dependencies
        print("📦 Installing dependencies with UV...")
        install_cmd = ["uv", "pip", "install", "-e", ".[dev]"]
        result = subprocess.run(install_cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            print("💡 Trying fallback installation...")
            
            # Fallback: install core dependencies only
            fallback_cmd = ["uv", "pip", "install", "-e", "."]
            result = subprocess.run(fallback_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ Fallback installation failed: {result.stderr}")
                return False
            else:
                print("✅ Core dependencies installed (development tools skipped)")
        else:
            print("✅ All dependencies installed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up environment: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("📁 Creating application directories...")
    directories = ["logs", "uploads", ".uv-cache"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def check_environment_file():
    """Check and create environment file if needed."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from template...")
        shutil.copy(env_example, env_file)
        print("✅ Environment file created")
        print("💡 Please update .env with your actual configuration")
    elif env_file.exists():
        print("✅ Environment file found")
    else:
        print("⚠️ No environment file found. Using defaults.")


def run_tests():
    """Run basic application tests."""
    print("🧪 Running application tests...")
    
    try:
        # Run pytest with UV
        test_cmd = ["uv", "run", "pytest", "tests/", "-v", "--tb=short"]
        result = subprocess.run(test_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print(f"⚠️ Some tests failed: {result.stdout}")
            print("💡 Application will still start, but please review test failures")
            return True  # Don't block startup for test failures
            
    except Exception as e:
        print(f"⚠️ Could not run tests: {e}")
        return True  # Don't block startup


def start_server():
    """Start the FastAPI server using UV."""
    print("\n🚀 Starting FastAPI server...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🏥 Health Check: http://localhost:8000/health")
    print("🔧 Admin Panel: http://localhost:8000/admin")
    print("Press Ctrl+C to stop the server")
    print("-" * 70)
    
    try:
        # Start server with UV
        server_cmd = [
            "uv", "run", "uvicorn", "app.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ]
        
        subprocess.run(server_cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False
    
    return True


def main():
    """Main startup function."""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check/install UV
    if not check_uv_installation():
        return 1
    
    # Set up environment
    if not setup_environment():
        return 1
    
    # Create directories
    create_directories()
    
    # Check environment file
    check_environment_file()
    
    # Run tests
    run_tests()
    
    print("\n✅ All setup completed! Starting server...")
    print("-" * 70)
    
    # Start server
    if not start_server():
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
