#!/usr/bin/env python3
"""
Simple test script to validate the FastAPI application startup.
This script tests the core components without external dependencies.
"""
import sys
import os
import traceback
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that core modules can be imported."""
    print("🔍 Testing core imports...")
    
    try:
        # Test Pydantic imports
        from pydantic import BaseModel, field_validator
        from pydantic_settings import BaseSettings
        print("✅ Pydantic imports successful")
        
        # Test FastAPI imports
        from fastapi import FastAPI
        print("✅ FastAPI imports successful")
        
        # Test app configuration
        from app.config.settings import Settings, get_settings
        print("✅ Settings imports successful")
        
        # Test schemas
        from app.schemas.user import UserCreate, UserResponse
        from app.schemas.auth import UserLogin, TokenResponse
        print("✅ Schema imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False


def test_settings():
    """Test settings configuration."""
    print("\n🔧 Testing settings configuration...")
    
    try:
        # Set minimal environment variables
        os.environ['SECRET_KEY'] = 'test-secret-key-for-testing-only-must-be-32-chars-long'
        os.environ['DATABASE_URL'] = 'sqlite:///./test.db'
        
        from app.config.settings import get_settings
        settings = get_settings()
        
        print(f"✅ Settings loaded: {settings.app_name}")
        print(f"✅ Database URL: {settings.database_url}")
        print(f"✅ Environment: {settings.environment}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings error: {e}")
        traceback.print_exc()
        return False


def test_fastapi_app():
    """Test FastAPI application creation."""
    print("\n🚀 Testing FastAPI application creation...")
    
    try:
        from app.main import app
        
        print(f"✅ FastAPI app created: {type(app)}")
        print(f"✅ App title: {app.title}")
        
        # Test that routes are registered
        routes = [route.path for route in app.routes]
        print(f"✅ Routes registered: {len(routes)} routes")
        
        # Check for key routes
        expected_routes = ["/health", "/docs", "/openapi.json"]
        for route in expected_routes:
            if any(route in r for r in routes):
                print(f"✅ Route found: {route}")
            else:
                print(f"⚠️ Route missing: {route}")
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI app error: {e}")
        traceback.print_exc()
        return False


def test_database_models():
    """Test database models."""
    print("\n🗄️ Testing database models...")
    
    try:
        from app.models.user import User, UserRole
        from app.models.store import Store
        
        print("✅ User model imported")
        print("✅ Store model imported")
        print(f"✅ UserRole enum: {list(UserRole)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database models error: {e}")
        traceback.print_exc()
        return False


def test_pydantic_schemas():
    """Test Pydantic schema validation."""
    print("\n📋 Testing Pydantic schemas...")
    
    try:
        from app.schemas.user import UserCreate
        from app.schemas.auth import UserLogin
        
        # Test schema creation
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User"
        }
        
        user_schema = UserCreate(**user_data)
        print(f"✅ UserCreate schema: {user_schema.email}")
        
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        login_schema = UserLogin(**login_data)
        print(f"✅ UserLogin schema: {login_schema.email}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema validation error: {e}")
        traceback.print_exc()
        return False


def test_health_endpoint():
    """Test health endpoint functionality."""
    print("\n🏥 Testing health endpoint...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        response = client.get("/health")
        
        print(f"✅ Health endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health response: {data}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🧪 FastAPI Application Startup Test")
    print("=" * 50)
    
    tests = [
        ("Core Imports", test_imports),
        ("Settings Configuration", test_settings),
        ("FastAPI Application", test_fastapi_app),
        ("Database Models", test_database_models),
        ("Pydantic Schemas", test_pydantic_schemas),
        ("Health Endpoint", test_health_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Application is ready to start.")
        return 0
    else:
        print("⚠️ Some tests failed. Please fix the issues before starting the application.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
