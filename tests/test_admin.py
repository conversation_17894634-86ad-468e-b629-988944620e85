"""
Tests for admin functionality.
"""
import pytest
from fastapi import status

from app.models.user import User, UserRole
from app.models.store import Store


class TestAdminUserManagement:
    """Test admin user management functionality."""
    
    def test_get_all_users(self, client, superadmin_headers, test_user):
        """Test getting all users as superadmin."""
        response = client.get("/api/v1/admin/users", headers=superadmin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check if test user is in the list
        user_emails = [u["email"] for u in data]
        assert test_user.email in user_emails
    
    def test_get_all_users_unauthorized(self, client, auth_headers):
        """Test getting all users without admin privileges."""
        response = client.get("/api/v1/admin/users", headers=auth_headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_update_user_role(self, client, superadmin_headers, test_user, db_session):
        """Test updating user role."""
        update_data = {"role": "admin"}
        
        response = client.put(f"/api/v1/admin/users/{test_user.id}/role", 
                            json=update_data, headers=superadmin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["role"] == "admin"
        
        # Verify in database
        db_session.refresh(test_user)
        assert test_user.role == UserRole.ADMIN
    
    def test_deactivate_user(self, client, superadmin_headers, test_user, db_session):
        """Test deactivating a user."""
        response = client.post(f"/api/v1/admin/users/{test_user.id}/deactivate", 
                             headers=superadmin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "deactivated" in response.json()["message"]
        
        # Verify in database
        db_session.refresh(test_user)
        assert test_user.is_active is False
    
    def test_activate_user(self, client, superadmin_headers, db_session):
        """Test activating a user."""
        # Create inactive user
        inactive_user = User(
            email="<EMAIL>",
            password_hash="$2b$12$test",
            first_name="Inactive",
            last_name="User",
            role=UserRole.USER,
            is_active=False,
            is_verified=True
        )
        db_session.add(inactive_user)
        db_session.commit()
        db_session.refresh(inactive_user)
        
        response = client.post(f"/api/v1/admin/users/{inactive_user.id}/activate", 
                             headers=superadmin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "activated" in response.json()["message"]
        
        # Verify in database
        db_session.refresh(inactive_user)
        assert inactive_user.is_active is True


class TestAdminStoreManagement:
    """Test admin store management functionality."""
    
    def test_create_store(self, client, admin_headers):
        """Test creating a new store."""
        store_data = {
            "name": "new_store",
            "display_name": "New Store",
            "base_url": "https://newstore.com",
            "description": "A new test store",
            "is_active": True
        }
        
        response = client.post("/api/v1/admin/stores", 
                             json=store_data, headers=admin_headers)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == store_data["name"]
        assert data["display_name"] == store_data["display_name"]
        assert data["base_url"] == store_data["base_url"]
    
    def test_get_all_stores(self, client, admin_headers, db_session):
        """Test getting all stores."""
        # Create test store
        store = Store(
            name="test_store",
            display_name="Test Store",
            base_url="https://test.com",
            is_active=True
        )
        db_session.add(store)
        db_session.commit()
        
        response = client.get("/api/v1/admin/stores", headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
    
    def test_update_store(self, client, admin_headers, db_session):
        """Test updating a store."""
        # Create test store
        store = Store(
            name="test_store",
            display_name="Test Store",
            base_url="https://test.com",
            is_active=True
        )
        db_session.add(store)
        db_session.commit()
        db_session.refresh(store)
        
        update_data = {
            "display_name": "Updated Store Name",
            "description": "Updated description"
        }
        
        response = client.put(f"/api/v1/admin/stores/{store.id}", 
                            json=update_data, headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["display_name"] == update_data["display_name"]
        assert data["description"] == update_data["description"]
    
    def test_delete_store(self, client, admin_headers, db_session):
        """Test deleting a store."""
        # Create test store
        store = Store(
            name="test_store",
            display_name="Test Store",
            base_url="https://test.com",
            is_active=True
        )
        db_session.add(store)
        db_session.commit()
        db_session.refresh(store)
        
        response = client.delete(f"/api/v1/admin/stores/{store.id}", 
                               headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "deleted" in response.json()["message"]
        
        # Verify soft delete
        db_session.refresh(store)
        assert store.is_active is False


class TestAdminDashboard:
    """Test admin dashboard functionality."""
    
    def test_get_dashboard_stats(self, client, admin_headers):
        """Test getting dashboard statistics."""
        response = client.get("/api/v1/admin/dashboard/stats", headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "total_users" in data
        assert "total_products" in data
        assert "total_stores" in data
        assert "active_users" in data
        assert isinstance(data["total_users"], int)
        assert isinstance(data["total_products"], int)
    
    def test_get_recent_activities(self, client, admin_headers):
        """Test getting recent activities."""
        response = client.get("/api/v1/admin/dashboard/activities", headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
    
    def test_dashboard_unauthorized(self, client, auth_headers):
        """Test dashboard access without admin privileges."""
        response = client.get("/api/v1/admin/dashboard/stats", headers=auth_headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestAdminSystemManagement:
    """Test admin system management functionality."""
    
    def test_system_health_check(self, client, admin_headers):
        """Test system health check for admins."""
        response = client.get("/api/v1/admin/system/health", headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "status" in data
        assert "database" in data
        assert "redis" in data
        assert "timestamp" in data
    
    def test_clear_cache(self, client, admin_headers):
        """Test clearing system cache."""
        response = client.post("/api/v1/admin/system/clear-cache", headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "cache cleared" in response.json()["message"]
    
    def test_system_info(self, client, superadmin_headers):
        """Test getting system information."""
        response = client.get("/api/v1/admin/system/info", headers=superadmin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "version" in data
        assert "python_version" in data
        assert "environment" in data
    
    def test_system_info_admin_forbidden(self, client, admin_headers):
        """Test system info access with admin (not superadmin)."""
        response = client.get("/api/v1/admin/system/info", headers=admin_headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.slow
class TestAdminBulkOperations:
    """Test admin bulk operations."""
    
    def test_bulk_user_operations(self, client, superadmin_headers, db_session):
        """Test bulk user operations."""
        # Create multiple test users
        users = []
        for i in range(3):
            user = User(
                email=f"bulk{i}@test.com",
                password_hash="$2b$12$test",
                first_name=f"Bulk{i}",
                last_name="User",
                role=UserRole.USER,
                is_active=True,
                is_verified=True
            )
            users.append(user)
            db_session.add(user)
        
        db_session.commit()
        for user in users:
            db_session.refresh(user)
        
        # Test bulk deactivation
        user_ids = [user.id for user in users]
        bulk_data = {"user_ids": user_ids, "action": "deactivate"}
        
        response = client.post("/api/v1/admin/users/bulk", 
                             json=bulk_data, headers=superadmin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["processed"] == len(user_ids)
        
        # Verify all users are deactivated
        for user in users:
            db_session.refresh(user)
            assert user.is_active is False
