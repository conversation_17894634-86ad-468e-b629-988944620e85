"""
Tests for Amazon Product Advertising API integration.
"""
import pytest
from unittest.mock import Mock, patch
from fastapi import status


class TestAmazonAPIConfiguration:
    """Test Amazon API configuration and setup."""
    
    def test_amazon_config_loaded(self, client):
        """Test that Amazon API configuration is properly loaded."""
        # This tests the configuration without making actual API calls
        from app.config.settings import get_settings
        
        settings = get_settings()
        
        # Check that Amazon settings are configured
        assert hasattr(settings, 'AMAZON_ACCESS_KEY')
        assert hasattr(settings, 'AMAZON_SECRET_KEY')
        assert hasattr(settings, 'AMAZON_PARTNER_TAG')
        assert hasattr(settings, 'AMAZON_HOST')
        assert hasattr(settings, 'AMAZON_REGION')
        
        # Should be configured for India region
        assert settings.AMAZON_HOST == "webservices.amazon.in"
        assert settings.AMAZON_REGION == "ap-south-1"


class TestAmazonProductSearch:
    """Test Amazon product search functionality."""
    
    @patch('app.services.amazon_service.AmazonService.search_products')
    def test_search_products_success(self, mock_search, client):
        """Test successful product search."""
        # Mock successful API response
        mock_search.return_value = {
            "products": [
                {
                    "asin": "B08N5WRWNW",
                    "title": "Test Product",
                    "price": "₹999",
                    "image_url": "https://example.com/image.jpg",
                    "rating": 4.5,
                    "review_count": 100
                }
            ],
            "total_results": 1
        }
        
        response = client.get("/api/v1/amazon/search", params={
            "query": "smartphone",
            "category": "Electronics"
        })
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "products" in data
        assert len(data["products"]) == 1
        assert data["products"][0]["asin"] == "B08N5WRWNW"
    
    @patch('app.services.amazon_service.AmazonService.search_products')
    def test_search_products_api_error(self, mock_search, client):
        """Test product search with API error."""
        # Mock API error
        mock_search.side_effect = Exception("API rate limit exceeded")
        
        response = client.get("/api/v1/amazon/search", params={
            "query": "smartphone"
        })
        
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        data = response.json()
        assert "error" in data
    
    def test_search_products_invalid_params(self, client):
        """Test product search with invalid parameters."""
        # Missing required query parameter
        response = client.get("/api/v1/amazon/search")
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_search_products_empty_query(self, client):
        """Test product search with empty query."""
        response = client.get("/api/v1/amazon/search", params={
            "query": ""
        })
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestAmazonProductDetails:
    """Test Amazon product details functionality."""
    
    @patch('app.services.amazon_service.AmazonService.get_product_details')
    def test_get_product_details_success(self, mock_details, client):
        """Test successful product details retrieval."""
        # Mock successful API response
        mock_details.return_value = {
            "asin": "B08N5WRWNW",
            "title": "Test Smartphone",
            "description": "A great smartphone for testing",
            "price": "₹15,999",
            "original_price": "₹19,999",
            "discount": "20%",
            "images": [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ],
            "rating": 4.3,
            "review_count": 1250,
            "features": [
                "6GB RAM",
                "128GB Storage",
                "48MP Camera"
            ],
            "availability": "In Stock",
            "delivery_info": "Free delivery by tomorrow"
        }
        
        response = client.get("/api/v1/amazon/product/B08N5WRWNW")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["asin"] == "B08N5WRWNW"
        assert data["title"] == "Test Smartphone"
        assert "features" in data
        assert len(data["images"]) == 2
    
    @patch('app.services.amazon_service.AmazonService.get_product_details')
    def test_get_product_details_not_found(self, mock_details, client):
        """Test product details for non-existent product."""
        # Mock product not found
        mock_details.return_value = None
        
        response = client.get("/api/v1/amazon/product/INVALID_ASIN")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "not found" in data["error"]["message"]
    
    def test_get_product_details_invalid_asin(self, client):
        """Test product details with invalid ASIN format."""
        response = client.get("/api/v1/amazon/product/invalid")
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestAmazonCategories:
    """Test Amazon categories functionality."""
    
    @patch('app.services.amazon_service.AmazonService.get_categories')
    def test_get_categories_success(self, mock_categories, client):
        """Test successful categories retrieval."""
        # Mock categories response
        mock_categories.return_value = [
            {
                "id": "Electronics",
                "name": "Electronics",
                "subcategories": [
                    {"id": "Smartphones", "name": "Smartphones"},
                    {"id": "Laptops", "name": "Laptops"}
                ]
            },
            {
                "id": "Fashion",
                "name": "Fashion",
                "subcategories": [
                    {"id": "Clothing", "name": "Clothing"},
                    {"id": "Shoes", "name": "Shoes"}
                ]
            }
        ]
        
        response = client.get("/api/v1/amazon/categories")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 2
        assert data[0]["id"] == "Electronics"
        assert len(data[0]["subcategories"]) == 2
    
    @patch('app.services.amazon_service.AmazonService.get_categories')
    def test_get_categories_cache(self, mock_categories, client):
        """Test categories caching."""
        # Mock categories response
        mock_categories.return_value = [
            {"id": "Electronics", "name": "Electronics", "subcategories": []}
        ]
        
        # Make multiple requests
        response1 = client.get("/api/v1/amazon/categories")
        response2 = client.get("/api/v1/amazon/categories")
        
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        # Should only call the service once due to caching
        assert mock_categories.call_count <= 2  # Allow for some cache misses in tests


class TestAmazonPriceTracking:
    """Test Amazon price tracking functionality."""
    
    @patch('app.services.amazon_service.AmazonService.get_price_history')
    def test_get_price_history_success(self, mock_history, client, auth_headers):
        """Test successful price history retrieval."""
        # Mock price history response
        mock_history.return_value = {
            "asin": "B08N5WRWNW",
            "current_price": "₹15,999",
            "price_history": [
                {"date": "2024-01-01", "price": "₹19,999"},
                {"date": "2024-01-15", "price": "₹17,999"},
                {"date": "2024-02-01", "price": "₹15,999"}
            ],
            "lowest_price": "₹15,999",
            "highest_price": "₹19,999",
            "average_price": "₹17,999"
        }
        
        response = client.get("/api/v1/amazon/product/B08N5WRWNW/price-history", 
                            headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["asin"] == "B08N5WRWNW"
        assert len(data["price_history"]) == 3
        assert "lowest_price" in data
    
    def test_get_price_history_unauthorized(self, client):
        """Test price history without authentication."""
        response = client.get("/api/v1/amazon/product/B08N5WRWNW/price-history")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @patch('app.services.amazon_service.AmazonService.track_price')
    def test_track_price_success(self, mock_track, client, auth_headers):
        """Test successful price tracking setup."""
        # Mock tracking response
        mock_track.return_value = {
            "asin": "B08N5WRWNW",
            "target_price": "₹14,999",
            "current_price": "₹15,999",
            "tracking_active": True
        }
        
        track_data = {
            "asin": "B08N5WRWNW",
            "target_price": "14999"
        }
        
        response = client.post("/api/v1/amazon/track-price", 
                             json=track_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["tracking_active"] is True
        assert data["asin"] == "B08N5WRWNW"


class TestAmazonRateLimiting:
    """Test Amazon API rate limiting."""
    
    @patch('app.services.amazon_service.AmazonService.search_products')
    def test_rate_limiting_applied(self, mock_search, client):
        """Test that rate limiting is properly applied."""
        # Mock successful response
        mock_search.return_value = {"products": [], "total_results": 0}
        
        # Make multiple rapid requests
        responses = []
        for _ in range(5):
            response = client.get("/api/v1/amazon/search", params={
                "query": "test"
            })
            responses.append(response)
        
        # Some requests should succeed, some might be rate limited
        success_count = sum(1 for r in responses if r.status_code == status.HTTP_200_OK)
        rate_limited_count = sum(1 for r in responses if r.status_code == status.HTTP_429_TOO_MANY_REQUESTS)
        
        # At least some should succeed
        assert success_count > 0
        # Total should be all requests
        assert success_count + rate_limited_count == 5
    
    def test_rate_limit_headers(self, client):
        """Test that rate limit headers are included."""
        response = client.get("/api/v1/amazon/search", params={
            "query": "test"
        })
        
        # Should include rate limit headers
        assert "X-RateLimit-Limit" in response.headers or response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        if response.status_code == status.HTTP_200_OK:
            assert "X-RateLimit-Remaining" in response.headers


class TestAmazonErrorHandling:
    """Test Amazon API error handling."""
    
    @patch('app.services.amazon_service.AmazonService.search_products')
    def test_api_timeout_handling(self, mock_search, client):
        """Test handling of API timeouts."""
        # Mock timeout error
        from requests.exceptions import Timeout
        mock_search.side_effect = Timeout("Request timed out")
        
        response = client.get("/api/v1/amazon/search", params={
            "query": "test"
        })
        
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        data = response.json()
        assert "timeout" in data["error"]["message"].lower()
    
    @patch('app.services.amazon_service.AmazonService.search_products')
    def test_api_authentication_error(self, mock_search, client):
        """Test handling of API authentication errors."""
        # Mock authentication error
        mock_search.side_effect = Exception("Invalid credentials")
        
        response = client.get("/api/v1/amazon/search", params={
            "query": "test"
        })
        
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        data = response.json()
        assert "error" in data
    
    @patch('app.services.amazon_service.AmazonService.search_products')
    def test_api_quota_exceeded(self, mock_search, client):
        """Test handling of API quota exceeded."""
        # Mock quota exceeded error
        mock_search.side_effect = Exception("Quota exceeded")
        
        response = client.get("/api/v1/amazon/search", params={
            "query": "test"
        })
        
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE


@pytest.mark.integration
class TestAmazonAPIIntegration:
    """Integration tests for Amazon API (requires actual API credentials)."""
    
    @pytest.mark.skip(reason="Requires actual Amazon API credentials")
    def test_real_amazon_search(self, client):
        """Test real Amazon API search (skipped by default)."""
        response = client.get("/api/v1/amazon/search", params={
            "query": "smartphone",
            "category": "Electronics"
        })
        
        # This would test against real API
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
    
    @pytest.mark.skip(reason="Requires actual Amazon API credentials")
    def test_real_product_details(self, client):
        """Test real Amazon product details (skipped by default)."""
        # Use a known ASIN for testing
        response = client.get("/api/v1/amazon/product/B08N5WRWNW")
        
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND, status.HTTP_503_SERVICE_UNAVAILABLE]
