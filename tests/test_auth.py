"""
Tests for authentication functionality.
"""
import pytest
from fastapi import status

from app.models.user import User, UserRole
from app.utils.security import verify_password


class TestUserRegistration:
    """Test user registration functionality."""
    
    def test_register_user_success(self, client):
        """Test successful user registration."""
        user_data = {
            "email": "<EMAIL>",
            "password": "NewPassword123!",
            "first_name": "New",
            "last_name": "User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["first_name"] == user_data["first_name"]
        assert data["role"] == "user"
        assert data["is_active"] is True
        assert data["is_verified"] is False
    
    def test_register_user_duplicate_email(self, client, test_user):
        """Test registration with duplicate email."""
        user_data = {
            "email": test_user.email,
            "password": "NewPassword123!",
            "first_name": "Duplicate",
            "last_name": "User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert "already registered" in response.json()["error"]["message"]
    
    def test_register_user_weak_password(self, client):
        """Test registration with weak password."""
        user_data = {
            "email": "<EMAIL>",
            "password": "weak",
            "first_name": "Weak",
            "last_name": "Password"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_register_user_invalid_email(self, client):
        """Test registration with invalid email."""
        user_data = {
            "email": "invalid-email",
            "password": "ValidPassword123!",
            "first_name": "Invalid",
            "last_name": "Email"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestUserLogin:
    """Test user login functionality."""
    
    def test_login_success(self, client, test_user):
        """Test successful login."""
        login_data = {
            "email": test_user.email,
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
    
    def test_login_invalid_email(self, client):
        """Test login with invalid email."""
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid email or password" in response.json()["error"]["message"]
    
    def test_login_invalid_password(self, client, test_user):
        """Test login with invalid password."""
        login_data = {
            "email": test_user.email,
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid email or password" in response.json()["error"]["message"]
    
    def test_login_inactive_user(self, client, db_session):
        """Test login with inactive user."""
        # Create inactive user
        inactive_user = User(
            email="<EMAIL>",
            password_hash="$2b$12$test",
            role=UserRole.USER,
            is_active=False,
            is_verified=True
        )
        db_session.add(inactive_user)
        db_session.commit()
        
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "disabled" in response.json()["error"]["message"]


class TestTokenRefresh:
    """Test token refresh functionality."""
    
    def test_refresh_token_success(self, client, test_user):
        """Test successful token refresh."""
        # First login to get tokens
        login_response = client.post("/api/v1/auth/login", json={
            "email": test_user.email,
            "password": "testpassword123"
        })
        refresh_token = login_response.json()["refresh_token"]
        
        # Refresh token
        refresh_data = {"refresh_token": refresh_token}
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    def test_refresh_token_invalid(self, client):
        """Test refresh with invalid token."""
        refresh_data = {"refresh_token": "invalid_token"}
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestProtectedEndpoints:
    """Test protected endpoint access."""
    
    def test_access_protected_endpoint_with_token(self, client, auth_headers):
        """Test accessing protected endpoint with valid token."""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "email" in data
        assert "role" in data
    
    def test_access_protected_endpoint_without_token(self, client):
        """Test accessing protected endpoint without token."""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_access_protected_endpoint_invalid_token(self, client):
        """Test accessing protected endpoint with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestPasswordChange:
    """Test password change functionality."""
    
    def test_change_password_success(self, client, auth_headers, test_user, db_session):
        """Test successful password change."""
        password_data = {
            "current_password": "testpassword123",
            "new_password": "NewPassword123!"
        }
        
        response = client.post("/api/v1/auth/change-password", 
                             json=password_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify password was changed
        db_session.refresh(test_user)
        assert verify_password("NewPassword123!", test_user.password_hash)
    
    def test_change_password_wrong_current(self, client, auth_headers):
        """Test password change with wrong current password."""
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "NewPassword123!"
        }
        
        response = client.post("/api/v1/auth/change-password", 
                             json=password_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "incorrect" in response.json()["error"]["message"]
