"""
Test configuration and utilities for UV-based testing.
"""
import os
import sys
import tempfile
from pathlib import Path
from typing import Generator

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Add the app directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.main import app
from app.database.connection import get_db, Base
from app.models.user import User, UserRole
from app.utils.security import get_password_hash


class TestConfig:
    """Test configuration class."""
    
    # Test database settings
    TEST_DATABASE_URL = "sqlite:///./test.db"
    
    # Test environment settings
    ENVIRONMENT = "testing"
    DEBUG = True
    
    # Test security settings
    SECRET_KEY = "test-secret-key-for-testing-only"
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRE_MINUTES = 30
    
    # Test Amazon API settings (mock values)
    AMAZON_ACCESS_KEY = "test-access-key"
    AMAZON_SECRET_KEY = "test-secret-key"
    AMAZON_PARTNER_TAG = "test-partner-tag"
    AMAZON_HOST = "webservices.amazon.in"
    AMAZON_REGION = "ap-south-1"
    
    # Test Redis settings
    REDIS_URL = "redis://localhost:6379/1"  # Use different DB for tests
    
    # Disable external services in tests
    ENABLE_AMAZON_API = False
    ENABLE_SCRAPING = False
    ENABLE_REDIS = False


def setup_test_environment():
    """Set up test environment variables."""
    test_env_vars = {
        "ENVIRONMENT": "testing",
        "DEBUG": "true",
        "DATABASE_URL": TestConfig.TEST_DATABASE_URL,
        "SECRET_KEY": TestConfig.SECRET_KEY,
        "AMAZON_ACCESS_KEY": TestConfig.AMAZON_ACCESS_KEY,
        "AMAZON_SECRET_KEY": TestConfig.AMAZON_SECRET_KEY,
        "AMAZON_PARTNER_TAG": TestConfig.AMAZON_PARTNER_TAG,
        "AMAZON_HOST": TestConfig.AMAZON_HOST,
        "AMAZON_REGION": TestConfig.AMAZON_REGION,
        "REDIS_URL": TestConfig.REDIS_URL,
        "ENABLE_AMAZON_API": "false",
        "ENABLE_SCRAPING": "false",
        "ENABLE_REDIS": "false",
    }
    
    for key, value in test_env_vars.items():
        os.environ[key] = value


def create_test_database() -> Generator:
    """Create a test database for testing."""
    # Create temporary database file
    db_fd, db_path = tempfile.mkstemp(suffix=".db")
    database_url = f"sqlite:///{db_path}"
    
    try:
        # Create engine and tables
        engine = create_engine(
            database_url, 
            connect_args={"check_same_thread": False}
        )
        Base.metadata.create_all(bind=engine)
        
        yield engine
        
    finally:
        # Cleanup
        os.close(db_fd)
        if os.path.exists(db_path):
            os.unlink(db_path)


def create_test_users(db_session) -> dict:
    """Create test users for testing."""
    users = {}
    
    # Regular user
    regular_user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("testpassword123"),
        first_name="Test",
        last_name="User",
        role=UserRole.USER,
        is_active=True,
        is_verified=True
    )
    db_session.add(regular_user)
    users["user"] = regular_user
    
    # Admin user
    admin_user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("adminpassword123"),
        first_name="Admin",
        last_name="User",
        role=UserRole.ADMIN,
        is_active=True,
        is_verified=True
    )
    db_session.add(admin_user)
    users["admin"] = admin_user
    
    # Superadmin user
    superadmin_user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("superadminpassword123"),
        first_name="Super",
        last_name="Admin",
        role=UserRole.SUPERADMIN,
        is_active=True,
        is_verified=True
    )
    db_session.add(superadmin_user)
    users["superadmin"] = superadmin_user
    
    db_session.commit()
    
    # Refresh all users
    for user in users.values():
        db_session.refresh(user)
    
    return users


def get_auth_headers(client: TestClient, email: str, password: str) -> dict:
    """Get authentication headers for a user."""
    response = client.post("/api/v1/auth/login", json={
        "email": email,
        "password": password
    })
    
    if response.status_code != 200:
        raise Exception(f"Failed to login: {response.json()}")
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


class TestDataFactory:
    """Factory for creating test data."""
    
    @staticmethod
    def create_product_data(store_id: int = 1) -> dict:
        """Create sample product data."""
        return {
            "title": "Test Product",
            "description": "This is a test product description",
            "short_description": "Test product",
            "store_id": store_id,
            "brand": "Test Brand",
            "is_available": True,
            "is_featured": False,
            "price": "999.99",
            "original_price": "1299.99",
            "discount_percentage": 23.08
        }
    
    @staticmethod
    def create_store_data() -> dict:
        """Create sample store data."""
        return {
            "name": "test_store",
            "display_name": "Test Store",
            "base_url": "https://test-store.com",
            "description": "A test store for testing purposes",
            "is_active": True
        }
    
    @staticmethod
    def create_user_data() -> dict:
        """Create sample user data."""
        return {
            "email": "<EMAIL>",
            "password": "NewPassword123!",
            "first_name": "New",
            "last_name": "User",
            "phone": "+1234567890",
            "city": "Test City"
        }


class MockServices:
    """Mock services for testing."""
    
    @staticmethod
    def mock_amazon_service():
        """Mock Amazon service responses."""
        return {
            "search_products": {
                "products": [
                    {
                        "asin": "B08N5WRWNW",
                        "title": "Test Product",
                        "price": "₹999",
                        "image_url": "https://example.com/image.jpg",
                        "rating": 4.5,
                        "review_count": 100
                    }
                ],
                "total_results": 1
            },
            "get_product_details": {
                "asin": "B08N5WRWNW",
                "title": "Test Product",
                "description": "Test description",
                "price": "₹999",
                "images": ["https://example.com/image.jpg"],
                "rating": 4.5,
                "review_count": 100
            }
        }
    
    @staticmethod
    def mock_scraping_service():
        """Mock scraping service responses."""
        return {
            "scrape_product": {
                "title": "Scraped Product",
                "price": "₹1299",
                "description": "Scraped description",
                "images": ["https://example.com/scraped.jpg"],
                "availability": "In Stock"
            }
        }


def cleanup_test_data(db_session):
    """Clean up test data after tests."""
    try:
        # Delete test data in reverse order of dependencies
        db_session.execute("DELETE FROM user_favorites")
        db_session.execute("DELETE FROM product_images")
        db_session.execute("DELETE FROM reviews")
        db_session.execute("DELETE FROM products")
        db_session.execute("DELETE FROM stores")
        db_session.execute("DELETE FROM users WHERE email LIKE '%test.com'")
        db_session.commit()
    except Exception as e:
        print(f"Warning: Failed to cleanup test data: {e}")
        db_session.rollback()


# Test markers for different types of tests
pytest_markers = {
    "unit": "Unit tests that don't require external dependencies",
    "integration": "Integration tests that test component interactions",
    "api": "API endpoint tests",
    "database": "Tests that require database access",
    "auth": "Authentication and authorization tests",
    "slow": "Slow tests that take more than 1 second",
    "external": "Tests that require external services",
    "mock": "Tests that use mocked services"
}


def register_test_markers():
    """Register custom test markers."""
    for marker, description in pytest_markers.items():
        pytest.mark.__dict__[marker] = pytest.mark.__dict__.get(marker, pytest.mark.slow)


# Performance test utilities
class PerformanceTimer:
    """Simple performance timer for tests."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        self.end_time = time.time()
    
    @property
    def elapsed(self) -> float:
        """Get elapsed time in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0


# Test data validation utilities
def validate_response_structure(response_data: dict, required_fields: list) -> bool:
    """Validate that response has required fields."""
    for field in required_fields:
        if field not in response_data:
            return False
    return True


def validate_user_response(user_data: dict) -> bool:
    """Validate user response structure."""
    required_fields = ["id", "email", "first_name", "last_name", "role", "is_active"]
    return validate_response_structure(user_data, required_fields)


def validate_product_response(product_data: dict) -> bool:
    """Validate product response structure."""
    required_fields = ["id", "title", "store_id", "is_available", "created_at"]
    return validate_response_structure(product_data, required_fields)


# Initialize test environment when module is imported
setup_test_environment()
register_test_markers()
