"""
Tests for health check and monitoring endpoints.
"""
import pytest
from fastapi import status


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_basic_health_check(self, client):
        """Test basic health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert data["status"] == "healthy"
    
    def test_detailed_health_check(self, client):
        """Test detailed health check endpoint."""
        response = client.get("/api/v1/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "status" in data
        assert "checks" in data
        assert "timestamp" in data
        assert "uptime" in data
        
        # Check individual service checks
        checks = data["checks"]
        assert "database" in checks
        assert "redis" in checks
        
        # Each check should have status
        for service, check in checks.items():
            assert "status" in check
            assert check["status"] in ["healthy", "unhealthy", "unknown"]
    
    def test_readiness_check(self, client):
        """Test readiness check endpoint."""
        response = client.get("/api/v1/health/ready")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "ready" in data
        assert "services" in data
        assert isinstance(data["ready"], bool)
    
    def test_liveness_check(self, client):
        """Test liveness check endpoint."""
        response = client.get("/api/v1/health/live")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "alive" in data
        assert data["alive"] is True


class TestMetricsEndpoints:
    """Test metrics and monitoring endpoints."""
    
    def test_metrics_endpoint(self, client):
        """Test Prometheus metrics endpoint."""
        response = client.get("/metrics")
        
        # Should return metrics in Prometheus format
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "text/plain; version=0.0.4; charset=utf-8"
        
        # Check for basic metrics
        content = response.text
        assert "http_requests_total" in content or "process_" in content
    
    def test_application_metrics(self, client):
        """Test application-specific metrics."""
        response = client.get("/api/v1/metrics")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check for application metrics
        assert "requests" in data
        assert "database" in data
        assert "cache" in data
        
        # Each metric should have basic structure
        for metric_name, metric_data in data.items():
            assert isinstance(metric_data, dict)


class TestDatabaseHealthCheck:
    """Test database-specific health checks."""
    
    def test_database_connection(self, client):
        """Test database connection health."""
        response = client.get("/api/v1/health/database")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "status" in data
        assert "connection_pool" in data
        assert "query_time" in data
        
        # Should be able to connect
        assert data["status"] == "healthy"
        assert isinstance(data["query_time"], (int, float))
    
    def test_database_migration_status(self, client):
        """Test database migration status."""
        response = client.get("/api/v1/health/database/migrations")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "current_revision" in data
        assert "pending_migrations" in data
        assert isinstance(data["pending_migrations"], int)


class TestCacheHealthCheck:
    """Test cache (Redis) health checks."""
    
    def test_redis_connection(self, client):
        """Test Redis connection health."""
        response = client.get("/api/v1/health/cache")
        
        # Redis might not be available in test environment
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
        
        data = response.json()
        assert "status" in data
        
        if response.status_code == status.HTTP_200_OK:
            assert data["status"] == "healthy"
            assert "ping_time" in data
            assert "memory_usage" in data
        else:
            assert data["status"] == "unhealthy"
    
    def test_cache_operations(self, client):
        """Test cache operations health."""
        response = client.get("/api/v1/health/cache/operations")
        
        # Should work even if Redis is not available (fallback behavior)
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "set_operations" in data
        assert "get_operations" in data
        assert "hit_rate" in data


class TestExternalServicesHealth:
    """Test external services health checks."""
    
    def test_amazon_api_health(self, client):
        """Test Amazon PA-API health."""
        response = client.get("/api/v1/health/amazon")
        
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
        data = response.json()
        
        assert "status" in data
        assert "api_version" in data
        
        # Should include rate limit information
        if response.status_code == status.HTTP_200_OK:
            assert "rate_limit" in data
    
    @pytest.mark.slow
    def test_scraping_service_health(self, client):
        """Test scraping service health."""
        response = client.get("/api/v1/health/scraping")
        
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
        data = response.json()
        
        assert "status" in data
        assert "active_jobs" in data
        assert isinstance(data["active_jobs"], int)


class TestHealthCheckAuthentication:
    """Test health check authentication requirements."""
    
    def test_public_health_endpoints(self, client):
        """Test that basic health endpoints are public."""
        public_endpoints = [
            "/health",
            "/api/v1/health",
            "/api/v1/health/ready",
            "/api/v1/health/live"
        ]
        
        for endpoint in public_endpoints:
            response = client.get(endpoint)
            # Should not require authentication
            assert response.status_code != status.HTTP_401_UNAUTHORIZED
    
    def test_detailed_health_requires_auth(self, client, admin_headers):
        """Test that detailed health endpoints require authentication."""
        protected_endpoints = [
            "/api/v1/health/database",
            "/api/v1/health/cache",
            "/api/v1/metrics"
        ]
        
        for endpoint in protected_endpoints:
            # Without auth
            response = client.get(endpoint)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            
            # With auth
            response = client.get(endpoint, headers=admin_headers)
            assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]


class TestHealthCheckCaching:
    """Test health check caching behavior."""
    
    def test_health_check_caching(self, client):
        """Test that health checks are properly cached."""
        # Make multiple requests
        responses = []
        for _ in range(3):
            response = client.get("/api/v1/health")
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
        
        # Check if caching headers are present
        last_response = responses[-1]
        # Should have cache control headers for performance
        assert "cache-control" in last_response.headers or "etag" in last_response.headers
    
    def test_health_check_cache_invalidation(self, client):
        """Test health check cache invalidation."""
        # First request
        response1 = client.get("/api/v1/health")
        assert response1.status_code == status.HTTP_200_OK
        
        # Second request (should be cached or fresh)
        response2 = client.get("/api/v1/health")
        assert response2.status_code == status.HTTP_200_OK
        
        # Both should have valid timestamps
        data1 = response1.json()
        data2 = response2.json()
        assert "timestamp" in data1
        assert "timestamp" in data2


@pytest.mark.integration
class TestHealthCheckIntegration:
    """Integration tests for health checks."""
    
    def test_full_health_check_flow(self, client):
        """Test complete health check flow."""
        # Start with basic health
        response = client.get("/health")
        assert response.status_code == status.HTTP_200_OK
        
        # Check readiness
        response = client.get("/api/v1/health/ready")
        assert response.status_code == status.HTTP_200_OK
        ready_data = response.json()
        
        # If ready, detailed health should work
        if ready_data.get("ready"):
            response = client.get("/api/v1/health")
            assert response.status_code == status.HTTP_200_OK
            
            health_data = response.json()
            assert health_data["status"] == "healthy"
    
    def test_health_check_under_load(self, client):
        """Test health checks under simulated load."""
        # Make multiple concurrent requests
        responses = []
        for _ in range(10):
            response = client.get("/api/v1/health")
            responses.append(response)
        
        # All should succeed
        success_count = sum(1 for r in responses if r.status_code == status.HTTP_200_OK)
        assert success_count >= 8  # Allow for some failures under load
