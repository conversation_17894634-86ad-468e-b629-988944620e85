"""
Tests for product management functionality.
"""
import pytest
from fastapi import status

from app.models.product import Product
from app.models.store import Store


class TestProductCreation:
    """Test product creation functionality."""
    
    @pytest.fixture
    def test_store(self, db_session):
        """Create a test store."""
        store = Store(
            name="test_store",
            display_name="Test Store",
            base_url="https://test-store.com",
            is_active=True
        )
        db_session.add(store)
        db_session.commit()
        db_session.refresh(store)
        return store
    
    def test_create_product_success(self, client, admin_headers, test_store):
        """Test successful product creation."""
        product_data = {
            "title": "Test Product",
            "description": "This is a test product",
            "store_id": test_store.id,
            "brand": "Test Brand",
            "is_available": True,
            "is_featured": False
        }
        
        response = client.post("/api/v1/products/", 
                             json=product_data, headers=admin_headers)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["title"] == product_data["title"]
        assert data["brand"] == product_data["brand"]
        assert data["is_available"] == product_data["is_available"]
        assert data["store"]["id"] == test_store.id
    
    def test_create_product_unauthorized(self, client, auth_headers, test_store):
        """Test product creation without admin privileges."""
        product_data = {
            "title": "Test Product",
            "store_id": test_store.id
        }
        
        response = client.post("/api/v1/products/", 
                             json=product_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_create_product_invalid_store(self, client, admin_headers):
        """Test product creation with invalid store ID."""
        product_data = {
            "title": "Test Product",
            "store_id": 99999  # Non-existent store
        }
        
        response = client.post("/api/v1/products/", 
                             json=product_data, headers=admin_headers)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "Store not found" in response.json()["error"]["message"]


class TestProductRetrieval:
    """Test product retrieval functionality."""
    
    @pytest.fixture
    def test_product(self, db_session, test_store):
        """Create a test product."""
        product = Product(
            title="Test Product",
            description="Test description",
            store_id=test_store.id,
            brand="Test Brand",
            is_available=True,
            is_active=True
        )
        db_session.add(product)
        db_session.commit()
        db_session.refresh(product)
        return product
    
    def test_get_product_success(self, client, test_product):
        """Test successful product retrieval."""
        response = client.get(f"/api/v1/products/{test_product.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == test_product.id
        assert data["title"] == test_product.title
        assert data["brand"] == test_product.brand
    
    def test_get_product_not_found(self, client):
        """Test retrieval of non-existent product."""
        response = client.get("/api/v1/products/99999")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found" in response.json()["error"]["message"]
    
    def test_list_products(self, client, test_product):
        """Test product listing."""
        response = client.get("/api/v1/products/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check if our test product is in the list
        product_ids = [p["id"] for p in data]
        assert test_product.id in product_ids
    
    def test_list_products_with_filters(self, client, test_product):
        """Test product listing with filters."""
        response = client.get("/api/v1/products/", params={
            "brand": test_product.brand,
            "is_available": True
        })
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        
        # All products should match the filter
        for product in data:
            assert product["brand"] == test_product.brand
            assert product["is_available"] is True
    
    def test_list_products_pagination(self, client):
        """Test product listing pagination."""
        response = client.get("/api/v1/products/", params={
            "skip": 0,
            "limit": 5
        })
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 5


class TestProductUpdate:
    """Test product update functionality."""
    
    def test_update_product_success(self, client, admin_headers, test_product):
        """Test successful product update."""
        update_data = {
            "title": "Updated Product Title",
            "brand": "Updated Brand",
            "is_featured": True
        }
        
        response = client.put(f"/api/v1/products/{test_product.id}", 
                            json=update_data, headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["brand"] == update_data["brand"]
        assert data["is_featured"] == update_data["is_featured"]
    
    def test_update_product_unauthorized(self, client, auth_headers, test_product):
        """Test product update without admin privileges."""
        update_data = {"title": "Updated Title"}
        
        response = client.put(f"/api/v1/products/{test_product.id}", 
                            json=update_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_update_product_not_found(self, client, admin_headers):
        """Test update of non-existent product."""
        update_data = {"title": "Updated Title"}
        
        response = client.put("/api/v1/products/99999", 
                            json=update_data, headers=admin_headers)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestProductDeletion:
    """Test product deletion functionality."""
    
    def test_delete_product_success(self, client, admin_headers, test_product, db_session):
        """Test successful product deletion (soft delete)."""
        response = client.delete(f"/api/v1/products/{test_product.id}", 
                               headers=admin_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "deleted successfully" in response.json()["message"]
        
        # Verify product is soft deleted
        db_session.refresh(test_product)
        assert test_product.is_active is False
    
    def test_delete_product_unauthorized(self, client, auth_headers, test_product):
        """Test product deletion without admin privileges."""
        response = client.delete(f"/api/v1/products/{test_product.id}", 
                               headers=auth_headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_delete_product_not_found(self, client, admin_headers):
        """Test deletion of non-existent product."""
        response = client.delete("/api/v1/products/99999", 
                               headers=admin_headers)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestProductSearch:
    """Test product search functionality."""
    
    def test_search_suggestions(self, client, test_product):
        """Test search suggestions endpoint."""
        response = client.get("/api/v1/products/search/suggestions", 
                            params={"q": "test"})
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "suggestions" in data
        assert isinstance(data["suggestions"], list)
    
    def test_search_suggestions_min_length(self, client):
        """Test search suggestions with query too short."""
        response = client.get("/api/v1/products/search/suggestions", 
                            params={"q": "a"})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
