"""
Tests for user management functionality.
"""
import pytest
from fastapi import status

from app.models.user import User, UserRole
from app.models.product import Product
from app.models.user_favorite import UserFavorite


class TestUserProfile:
    """Test user profile functionality."""
    
    def test_get_user_profile(self, client, auth_headers, test_user):
        """Test getting user profile."""
        response = client.get("/api/v1/users/profile", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["email"] == test_user.email
        assert data["first_name"] == test_user.first_name
        assert data["role"] == test_user.role.value
    
    def test_update_user_profile(self, client, auth_headers, test_user, db_session):
        """Test updating user profile."""
        update_data = {
            "first_name": "Updated",
            "last_name": "Name",
            "phone": "+1234567890",
            "city": "Test City"
        }
        
        response = client.put("/api/v1/users/profile", 
                            json=update_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["first_name"] == update_data["first_name"]
        assert data["last_name"] == update_data["last_name"]
        assert data["phone"] == update_data["phone"]
        assert data["city"] == update_data["city"]
        
        # Verify in database
        db_session.refresh(test_user)
        assert test_user.first_name == update_data["first_name"]
        assert test_user.phone == update_data["phone"]
    
    def test_get_profile_unauthorized(self, client):
        """Test getting profile without authentication."""
        response = client.get("/api/v1/users/profile")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestUserFavorites:
    """Test user favorites functionality."""
    
    @pytest.fixture
    def test_product(self, db_session):
        """Create a test product."""
        from app.models.store import Store
        
        # Create store first
        store = Store(
            name="test_store",
            display_name="Test Store",
            base_url="https://test.com",
            is_active=True
        )
        db_session.add(store)
        db_session.flush()
        
        # Create product
        product = Product(
            title="Test Product",
            store_id=store.id,
            is_active=True,
            is_available=True
        )
        db_session.add(product)
        db_session.commit()
        db_session.refresh(product)
        return product
    
    def test_add_to_favorites(self, client, auth_headers, test_product, test_user, db_session):
        """Test adding product to favorites."""
        response = client.post(f"/api/v1/users/favorites/{test_product.id}", 
                             headers=auth_headers)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert "added to favorites" in response.json()["message"]
        
        # Verify in database
        favorite = db_session.query(UserFavorite).filter(
            UserFavorite.user_id == test_user.id,
            UserFavorite.product_id == test_product.id
        ).first()
        assert favorite is not None
    
    def test_add_duplicate_favorite(self, client, auth_headers, test_product, test_user, db_session):
        """Test adding duplicate favorite."""
        # Add favorite first
        favorite = UserFavorite(user_id=test_user.id, product_id=test_product.id)
        db_session.add(favorite)
        db_session.commit()
        
        # Try to add again
        response = client.post(f"/api/v1/users/favorites/{test_product.id}", 
                             headers=auth_headers)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert "already in favorites" in response.json()["error"]["message"]
    
    def test_remove_from_favorites(self, client, auth_headers, test_product, test_user, db_session):
        """Test removing product from favorites."""
        # Add favorite first
        favorite = UserFavorite(user_id=test_user.id, product_id=test_product.id)
        db_session.add(favorite)
        db_session.commit()
        
        response = client.delete(f"/api/v1/users/favorites/{test_product.id}", 
                               headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "removed from favorites" in response.json()["message"]
        
        # Verify removed from database
        favorite = db_session.query(UserFavorite).filter(
            UserFavorite.user_id == test_user.id,
            UserFavorite.product_id == test_product.id
        ).first()
        assert favorite is None
    
    def test_remove_nonexistent_favorite(self, client, auth_headers, test_product):
        """Test removing non-existent favorite."""
        response = client.delete(f"/api/v1/users/favorites/{test_product.id}", 
                               headers=auth_headers)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found in favorites" in response.json()["error"]["message"]
    
    def test_get_favorites(self, client, auth_headers, test_product, test_user, db_session):
        """Test getting user's favorites."""
        # Add favorite
        favorite = UserFavorite(user_id=test_user.id, product_id=test_product.id)
        db_session.add(favorite)
        db_session.commit()
        
        response = client.get("/api/v1/users/favorites", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check if our test product is in favorites
        product_ids = [p["id"] for p in data]
        assert test_product.id in product_ids
    
    def test_check_favorite_status(self, client, auth_headers, test_product, test_user, db_session):
        """Test checking favorite status."""
        # Add favorite
        favorite = UserFavorite(user_id=test_user.id, product_id=test_product.id)
        db_session.add(favorite)
        db_session.commit()
        
        response = client.get(f"/api/v1/users/favorites/check/{test_product.id}", 
                            headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["is_favorite"] is True
    
    def test_check_non_favorite_status(self, client, auth_headers, test_product):
        """Test checking non-favorite status."""
        response = client.get(f"/api/v1/users/favorites/check/{test_product.id}", 
                            headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["is_favorite"] is False
    
    def test_favorites_unauthorized(self, client, test_product):
        """Test favorites operations without authentication."""
        # Test add to favorites
        response = client.post(f"/api/v1/users/favorites/{test_product.id}")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        # Test get favorites
        response = client.get("/api/v1/users/favorites")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        # Test remove from favorites
        response = client.delete(f"/api/v1/users/favorites/{test_product.id}")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestUserService:
    """Test user service functionality."""
    
    def test_user_service_get_by_email(self, db_session, test_user):
        """Test getting user by email."""
        from app.services.user_service import UserService
        
        user_service = UserService(db_session)
        found_user = user_service.get_user_by_email(test_user.email)
        
        assert found_user is not None
        assert found_user.id == test_user.id
        assert found_user.email == test_user.email
    
    def test_user_service_get_by_id(self, db_session, test_user):
        """Test getting user by ID."""
        from app.services.user_service import UserService
        
        user_service = UserService(db_session)
        found_user = user_service.get_user_by_id(test_user.id)
        
        assert found_user is not None
        assert found_user.id == test_user.id
        assert found_user.email == test_user.email
